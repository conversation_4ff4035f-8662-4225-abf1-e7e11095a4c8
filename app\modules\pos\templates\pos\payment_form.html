{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Paiement de la vente #{{ sale.reference }}</h5>
                </div>
                <div class="card-body">
                    <!-- Résumé de la vente -->
                    <div class="mb-4">
                        <h6 class="fw-bold">Résumé de la vente</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <th style="width: 150px">Total HT:</th>
                                        <td>{{ "%.2f"|format(sale.total_ht) }} €</td>
                                    </tr>
                                    <tr>
                                        <th>TVA ({{ "%.1f"|format(sale.tax_rate) }}%):</th>
                                        <td>{{ "%.2f"|format(sale.tax_amount) }} €</td>
                                    </tr>
                                    {% if sale.discount_amount > 0 %}
                                    <tr>
                                        <th>Réduction:</th>
                                        <td>-{{ "%.2f"|format(sale.discount_amount) }} €</td>
                                    </tr>
                                    {% endif %}
                                    <tr class="table-primary">
                                        <th>Total TTC:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                                    </tr>
                                    {% if sale.total_paid > 0 %}
                                    <tr>
                                        <th>Déjà payé:</th>
                                        <td>{{ "%.2f"|format(sale.total_paid) }} €</td>
                                    </tr>
                                    <tr class="table-success">
                                        <th>Reste à payer:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc - sale.total_paid) }} €</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Formulaire de paiement -->
                    <form method="POST" class="needs-validation" novalidate>
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.method.label(class="form-label") }}
                            {{ form.method(class="form-select") }}
                        </div>

                        <div class="mb-3">
                            {{ form.amount.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.amount(class="form-control", value=sale.total_ttc) }}
                                <span class="input-group-text">€</span>
                            </div>
                        </div>

                        <div class="mb-3" id="reference-field" style="display: none;">
                            {{ form.reference.label(class="form-label") }}
                            {{ form.reference(class="form-control") }}
                            <div class="form-text">Numéro de transaction, chèque, etc.</div>
                        </div>

                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('pos.index') }}" class="btn btn-outline-secondary">Annuler</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const methodSelect = document.getElementById('method');
    const referenceField = document.getElementById('reference-field');

    // Afficher/masquer le champ référence selon le mode de paiement
    function toggleReferenceField() {
        const method = methodSelect.value;
        if (method === 'card' || method === 'check') {
            referenceField.style.display = 'block';
        } else {
            referenceField.style.display = 'none';
        }
    }

    methodSelect.addEventListener('change', toggleReferenceField);
    toggleReferenceField(); // État initial

    // Validation du formulaire
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %} 