from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.modules.tables.models_table import Table, TableStatus, TableReservation
from app import db
from app.modules.tables.forms_table import TableForm, TableReservationForm
from datetime import datetime, timedelta
from app.utils.decorators import permission_required

from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_tables')
def index():
    owner_id = current_user.get_owner_id
    tables = Table.query.filter_by(owner_id=owner_id).order_by(Table.number).all()
    return render_template('tables/index.html', tables=tables)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def new():
    form = TableForm()
    if form.validate_on_submit():
        table = Table(
            owner_id=current_user.id,
            number=form.number.data,
            capacity=form.capacity.data,
            location=form.location.data
        )
        db.session.add(table)
        db.session.commit()
        flash('Table ajoutée avec succès!', 'success')
        return redirect(url_for('tables.index'))
    return render_template('tables/form.html', form=form, title="Nouvelle Table")

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def edit(id):
    table = Table.query.get_or_404(id)
    form = TableForm(obj=table)
    if form.validate_on_submit():
        table.number = form.number.data
        table.capacity = form.capacity.data
        table.location = form.location.data
        db.session.commit()
        flash('Table mise à jour avec succès!', 'success')
        return redirect(url_for('tables.index'))
    return render_template('tables/form.html', form=form, table=table, title="Modifier Table")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_tables')
def show(id):
    table = Table.query.get_or_404(id)
    today = datetime.now().date()
    return render_template('tables/show.html', table=table, today=today)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def delete(id):
    table = Table.query.get_or_404(id)
    if table.current_order_id:
        flash('Impossible de supprimer une table avec une commande en cours!', 'error')
        return redirect(url_for('tables.index'))
    db.session.delete(table)
    db.session.commit()
    flash('Table supprimée avec succès!', 'success')
    return redirect(url_for('tables.index'))

@bp.route('/<int:id>/status', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def update_status(id):
    table = Table.query.get_or_404(id)
    data = request.get_json()
    new_status = data.get('status')
    
    if new_status not in [status for status in dir(TableStatus) if not status.startswith('_')]:
        return jsonify({'success': False, 'message': 'Statut invalide'}), 400
        
    table.status = new_status
    db.session.commit()
    return jsonify({'success': True})

@bp.route('/<int:id>/reserve', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def reserve(id):
    table = Table.query.get_or_404(id)
    form = TableReservationForm()
    
    if form.validate_on_submit():
        # Vérifier si la table est déjà réservée pour cette période
        existing_reservation = TableReservation.query.filter(
            TableReservation.table_id == table.id,
            TableReservation.reservation_date <= form.reservation_date.data + timedelta(minutes=form.duration_minutes.data),
            TableReservation.reservation_date + timedelta(minutes=TableReservation.duration_minutes) >= form.reservation_date.data
        ).first()
        
        if existing_reservation:
            flash('Cette table est déjà réservée pour cette période!', 'error')
        else:
            reservation = TableReservation(
                table_id=table.id,
                customer_name=form.customer_name.data,
                customer_phone=form.customer_phone.data,
                number_of_guests=form.number_of_guests.data,
                reservation_date=form.reservation_date.data,
                duration_minutes=form.duration_minutes.data,
                notes=form.notes.data
            )
            db.session.add(reservation)
            db.session.commit()
            flash('Réservation créée avec succès!', 'success')
            return redirect(url_for('tables.show', id=table.id))
            
    return render_template('tables/reserve.html', form=form, table=table)

@bp.route('/reservations')
@login_required
@permission_required('can_manage_tables')
def reservations():
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    reservations = TableReservation.query.filter(
        TableReservation.reservation_date >= datetime.strptime(date, '%Y-%m-%d'),
        TableReservation.reservation_date < datetime.strptime(date, '%Y-%m-%d') + timedelta(days=1)
    ).order_by(TableReservation.reservation_date).all()
    return render_template('tables/reservations.html', reservations=reservations, selected_date=date)

@bp.route('/reservations/<int:id>/cancel', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def cancel_reservation(id):
    reservation = TableReservation.query.get_or_404(id)
    db.session.delete(reservation)
    db.session.commit()
    flash('Réservation annulée avec succès!', 'success')
    return redirect(url_for('tables.reservations')) 