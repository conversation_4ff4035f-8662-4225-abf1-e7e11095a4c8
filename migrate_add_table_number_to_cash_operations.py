#!/usr/bin/env python3
"""
Migration pour ajouter la colonne table_number à la table cash_operations
Compatible avec SQLite
"""

import sqlite3
import os

def migrate_database():
    # Chemin vers la base de données
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à {db_path}")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne table_number existe déjà
        cursor.execute("PRAGMA table_info(cash_operations)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'table_number' in columns:
            print("La colonne table_number existe déjà dans la table cash_operations")
            conn.close()
            return True
        
        print("Ajout de la colonne table_number à la table cash_operations...")
        
        # Ajouter la colonne table_number
        cursor.execute("ALTER TABLE cash_operations ADD COLUMN table_number VARCHAR(10)")
        
        # Valider les changements
        conn.commit()
        
        print("Migration réussie ! Colonne table_number ajoutée à la table cash_operations")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Erreur lors de la migration: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n✅ Migration terminée avec succès!")
        print("Vous pouvez maintenant redémarrer l'application.")
    else:
        print("\n❌ Échec de la migration.")
