from flask import Flask
from .extensions import db, migrate, login, csrf, redis_client
from app.modules.auth.models import Anonymous
# Register custom filters
from app.utils.filters import format_currency_filter, datetime_filter

def create_app(config_class=None):
    app = Flask(__name__)
    if config_class is not None:
        app.config.from_object(config_class)
    
    # Configuration pour traiter les URLs avec/sans slash de la même façon
    app.url_map.strict_slashes = False
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login.init_app(app)
    login.anonymous_user = Anonymous
    csrf.init_app(app)
    redis_client.init_app(app)

    # Register context processors
    from app.modules.cash_register.context_processors import inject_cash_register_forms
    app.context_processor(inject_cash_register_forms)
    
    # Configure login
    login.login_view = 'auth.login'
    login.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login.login_message_category = 'info'
    
    # Register custom filters
    app.jinja_env.filters['format_currency'] = format_currency_filter
    app.jinja_env.filters['datetime'] = datetime_filter

    # Register blueprints
    register_blueprints(app)

    from app import cli
    cli.init_app(app)  # Initialiser les commandes CLI

    return app

def register_blueprints(app):
    """Register all blueprints"""
    from app.modules.auth import bp as auth_bp
    from app.modules.admin import bp as admin_bp
    from app.modules.main import bp as main_bp
    from app.modules.pos import bp as pos_bp
    from app.modules.inventory import bp as inventory_bp
    from app.modules.reports import bp as reports_bp
    from app.modules.settings import bp as settings_bp
    from app.modules.customers import bp as customers_bp
    from app.modules.promotions import bp as promotions_bp
    from app.modules.tables import bp as tables_bp
    from app.modules.expenses import bp as expenses_bp
    from app.modules.cash_register import bp as cash_register_bp
    from app.modules.advanced import bp as advanced_bp
    
    # Enregistrement des blueprints
    app.register_blueprint(main_bp, url_prefix='/')
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(promotions_bp, url_prefix='/promotions')
    app.register_blueprint(tables_bp, url_prefix='/tables')
    app.register_blueprint(expenses_bp, url_prefix='/expenses')
    app.register_blueprint(cash_register_bp, url_prefix='/cash-register')
    app.register_blueprint(advanced_bp, url_prefix='/advanced')