from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, IntegerField, SelectField, DateTimeField, TextAreaField
from wtforms.validators import DataRequired, Optional, NumberRange, Length

class TableForm(FlaskForm):
    number = StringField('Numéro', validators=[DataRequired(), Length(max=10)])
    capacity = IntegerField('Capacité', validators=[DataRequired(), NumberRange(min=1)])
    location = SelectField('Emplacement', 
        choices=[
            ('interior', 'Intérieur'),
            ('terrace', 'Terrasse'),
            ('bar', 'Bar'),
            ('private', 'Salle privée')
        ],
        validators=[DataRequired()]
    )

class TableReservationForm(FlaskForm):
    customer_name = StringField('Nom du client', validators=[DataRequired(), Length(max=100)])
    customer_phone = StringField('Téléphone', validators=[DataRequired(), Length(max=20)])
    number_of_guests = IntegerField('Nombre de personnes', validators=[DataRequired(), NumberRange(min=1)])
    reservation_date = DateTimeField('Date et heure', format='%Y-%m-%d %H:%M', validators=[DataRequired()])
    duration_minutes = IntegerField('Durée (minutes)', 
        validators=[DataRequired(), NumberRange(min=30, max=480)],
        default=120
    )
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)]) 