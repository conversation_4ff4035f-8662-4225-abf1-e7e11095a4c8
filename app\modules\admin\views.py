from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.modules.auth.models import User, UserRole
from app.modules.pos.models_sale import Sale
from app.modules.expenses.models_expense import Expense
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.settings.models_settings import Settings
from app.utils import get_date_range, format_currency
from datetime import datetime, timedelta
from sqlalchemy import func, or_
from werkzeug.security import generate_password_hash
from . import bp
from flask_wtf import FlaskForm

@bp.route('/')
@login_required
def index():
    """Page principale d'administration"""
    if not current_user.is_owner:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    return render_template('admin/index.html',
                         title='Administration')

@bp.route('/owner/users')
@login_required
def owner_users():
    """Liste des utilisateurs (vue owner)"""
    if not current_user.is_owner and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    form = FlaskForm()  # Formulaire vide pour le CSRF
    
    # Si c'est un owner, il ne voit que ses utilisateurs
    if current_user.is_owner:
        users = User.query.filter_by(owner_id=current_user.id).all()
    else:  # Si c'est un admin système, il voit tous les utilisateurs
        users = User.query.all()
    
    # Calculate user statistics
    total_users_count = len(users)
    active_users_count = len([u for u in users if u.is_active])
    admin_users_count = len([u for u in users if u.role in [UserRole.OWNER, UserRole.ADMIN]])
    manager_users_count = len([u for u in users if u.role == UserRole.MANAGER])
    employee_users_count = len([u for u in users if u.role == UserRole.EMPLOYEE])
    cashier_users_count = len([u for u in users if u.role == UserRole.CASHIER])
    kitchen_users_count = len([u for u in users if u.role == UserRole.KITCHEN])
    
    # Calculate recent activity statistics
    seven_days_ago = datetime.utcnow() - timedelta(days=7)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    
    active_users_7d_count = len([u for u in users if u.is_active and u.last_login and u.last_login >= seven_days_ago])
    active_users_30d_count = len([u for u in users if u.is_active and u.last_login and u.last_login >= thirty_days_ago])
    
    # Calculate total actions in last 7 days
    sales_7d = db.session.query(Sale).filter(
        Sale.created_at >= seven_days_ago,
        Sale.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    expenses_7d = db.session.query(Expense).filter(
        Expense.date >= seven_days_ago,
        Expense.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    # Product activity includes both new products and updates
    products_7d = db.session.query(Product).filter(
        or_(Product.created_at >= seven_days_ago, Product.updated_at >= seven_days_ago),
        Product.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    # Ingredient activity is based on creation date and stock updates
    ingredients_7d = db.session.query(Ingredient).filter(
        Ingredient.created_at >= seven_days_ago,
        Ingredient.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    total_actions_7d = sales_7d + expenses_7d + products_7d + ingredients_7d
    
    # Calculate total actions in last 30 days
    sales_30d = db.session.query(Sale).filter(
        Sale.created_at >= thirty_days_ago,
        Sale.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    expenses_30d = db.session.query(Expense).filter(
        Expense.date >= thirty_days_ago,
        Expense.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    products_30d = db.session.query(Product).filter(
        or_(Product.created_at >= thirty_days_ago, Product.updated_at >= thirty_days_ago),
        Product.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    ingredients_30d = db.session.query(Ingredient).filter(
        Ingredient.created_at >= thirty_days_ago,
        Ingredient.owner_id == current_user.id if current_user.is_owner else True
    ).count()
    
    total_actions_30d = sales_30d + expenses_30d + products_30d + ingredients_30d
    
    return render_template('admin/users/index.html',
                         title='Utilisateurs',
                         users=users,
                         total_users_count=total_users_count,
                         active_users_count=active_users_count,
                         admin_users_count=admin_users_count,
                         manager_users_count=manager_users_count,
                         employee_users_count=employee_users_count,
                         cashier_users_count=cashier_users_count,
                         kitchen_users_count=kitchen_users_count,
                         active_users_7d_count=active_users_7d_count,
                         active_users_30d_count=active_users_30d_count,
                         total_actions_7d=total_actions_7d,
                         total_actions_30d=total_actions_30d,
                         roles=UserRole,
                         form=form)

@bp.route('/owner/user/create', methods=['POST'])
@login_required
def create_owner_user():
    """Créer un nouvel utilisateur"""
    if not current_user.is_owner and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')
    
    # Vérifications pour OWNER et SYSTEM_ADMIN
    if current_user.is_owner and role == UserRole.OWNER.value:
        flash('Vous ne pouvez pas créer d\'autres propriétaires.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    if current_user.is_system_admin and role == UserRole.SYSTEM_ADMIN.value:
        flash('Vous ne pouvez pas créer d\'autres administrateurs système.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    # Vérifications d'unicité
    if User.query.filter_by(username=username).first():
        flash('Ce nom d\'utilisateur est déjà utilisé.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    if User.query.filter_by(email=email).first():
        flash('Cette adresse email est déjà utilisée.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    user = User(
        username=username,
        email=email,
        role=UserRole(role),
        is_active=True,
        created_by_id=current_user.id,
        owner_id=current_user.id  # L'owner sera soit le SYSTEM_ADMIN soit l'OWNER
    )
    user.set_password(password)
    
    db.session.add(user)
    db.session.commit()
    
    flash('Utilisateur créé avec succès.', 'success')
    return redirect(url_for('admin.owner_users'))

@bp.route('/owner/user/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_owner_user(id):
    """Modifier un utilisateur"""
    if not current_user.is_owner and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(id)
    
    # Un owner ne peut modifier que ses propres utilisateurs
    if current_user.is_owner and user.id not in [u.id for u in current_user.sales_as_owner[0].user.query.all()] if current_user.sales_as_owner else []:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    form = FlaskForm()  # Formulaire pour le CSRF
    
    if request.method == 'POST':
        if form.validate_on_submit():
            username = request.form.get('username')
            email = request.form.get('email')
            password = request.form.get('password')
            role = request.form.get('role')
            is_active = request.form.get('is_active') == 'on'
            
            # Un owner ne peut pas promouvoir un utilisateur en owner
            if current_user.is_owner and role == UserRole.OWNER.value:
                flash('Vous ne pouvez pas promouvoir un utilisateur en propriétaire.', 'error')
                return redirect(url_for('admin.edit_owner_user', id=id))
            
            # Vérifier si le nom d'utilisateur est déjà utilisé par un autre utilisateur
            existing_user = User.query.filter_by(username=username).first()
            if existing_user and existing_user.id != id:
                flash('Ce nom d\'utilisateur est déjà utilisé.', 'error')
                return redirect(url_for('admin.edit_owner_user', id=id))
            
            # Vérifier si l'email est déjà utilisé par un autre utilisateur
            existing_user = User.query.filter_by(email=email).first()
            if existing_user and existing_user.id != id:
                flash('Cette adresse email est déjà utilisée.', 'error')
                return redirect(url_for('admin.edit_owner_user', id=id))
            
            user.username = username
            user.email = email
            if password:  # Ne changer le mot de passe que s'il est fourni
                user.set_password(password)
            user.role = UserRole(role)
            user.is_active = is_active
            
            db.session.commit()
            
            flash('Utilisateur modifié avec succès.', 'success')
            return redirect(url_for('admin.owner_users'))
    
    return render_template('admin/users/edit.html',
                         title='Modifier l\'utilisateur',
                         user=user,
                         roles=UserRole,
                         form=form)

@bp.route('/owner/user/<int:id>/delete', methods=['POST'])
@login_required
def delete_owner_user(id):
    """Supprimer un utilisateur"""
    if not current_user.is_owner and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(id)
    
    # Un owner ne peut supprimer que ses propres utilisateurs
    if current_user.is_owner and user.id not in [u.id for u in current_user.sales_as_owner[0].user.query.all()] if current_user.sales_as_owner else []:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    # Prevent deleting the last owner
    if user.role == UserRole.OWNER and User.query.filter_by(role=UserRole.OWNER).count() <= 1:
        flash('Impossible de supprimer le dernier propriétaire.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    # Prevent self-deletion
    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte.', 'error')
        return redirect(url_for('admin.owner_users'))
    
    db.session.delete(user)
    db.session.commit()
    
    flash('Utilisateur supprimé avec succès.', 'success')
    return redirect(url_for('admin.owner_users'))

@bp.route('/users/import', methods=['POST'])
@login_required
def import_users():
    """Importer des utilisateurs depuis un fichier CSV"""
    if not current_user.is_owner:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    if 'file' not in request.files:
        flash('Aucun fichier sélectionné.', 'error')
        return redirect(url_for('admin.users'))
    
    file = request.files['file']
    if file.filename == '':
        flash('Aucun fichier sélectionné.', 'error')
        return redirect(url_for('admin.users'))
    
    if not file.filename.endswith('.csv'):
        flash('Le fichier doit être au format CSV.', 'error')
        return redirect(url_for('admin.users'))
    
    try:
        content = file.read().decode('utf-8')
        lines = content.splitlines()
        
        # Skip header row
        for line in lines[1:]:
            username, email, password, role = line.strip().split(',')
            
            if User.query.filter_by(username=username).first():
                flash(f'L\'utilisateur {username} existe déjà.', 'warning')
                continue
            
            if User.query.filter_by(email=email).first():
                flash(f'L\'email {email} est déjà utilisé.', 'warning')
                continue
            
            try:
                user_role = UserRole(role.upper())
            except ValueError:
                flash(f'Rôle invalide pour {username}: {role}', 'warning')
                continue
            
            user = User(
                username=username,
                email=email,
                role=user_role,
                is_active=True
            )
            user.set_password(password)
            db.session.add(user)
        
        db.session.commit()
        flash('Importation terminée avec succès.', 'success')
        
    except Exception as e:
        flash(f'Erreur lors de l\'importation: {str(e)}', 'error')
    
    return redirect(url_for('admin.users'))

@bp.route('/settings')
@login_required
def settings():
    """Paramètres système"""
    if current_user.role != UserRole.OWNER and not current_user.is_admin and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    return render_template('admin/settings.html',
                         title='Paramètres système',
                         settings=settings)

@bp.route('/owner/roles')
@login_required
def owner_roles():
    """Gestion des rôles (vue owner)"""
    if not current_user.is_owner and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    form = FlaskForm()  # Formulaire vide pour le CSRF
    
    # Récupérer les utilisateurs selon le type d'utilisateur connecté
    if current_user.is_owner:
        users = User.query.filter_by(created_by_id=current_user.id).all()
    else:  # Si c'est un admin système
        users = User.query.all()
    
    total_users = len(users)
    
    # Calculer le rôle le plus utilisé et le nombre d'utilisateurs par rôle
    role_counts = dict()
    for role in UserRole:
        role_counts[role] = 0
    max_users_count = 0
    
    for user in users:
        role = user.role
        role_counts[role] += 1
        max_users_count = max(max_users_count, role_counts[role])
    
    most_used_role = max(role_counts.items(), key=lambda x: x[1])[0] if role_counts else None
    
    # Liste des méthodes de permission
    permission_methods = [
        'can_access_reports',
        'can_manage_inventory',
        'can_process_sales',
        'can_access_kitchen',
        'can_manage_customers',
        'can_manage_promotions',
        'can_manage_tables',
        'can_manage_expenses',
        'can_access_cash_register',
        'can_manage_cash_register'
    ]
    
    # Calculer les permissions pour chaque rôle
    role_permissions = {}
    for role in UserRole:
        test_user = User(role=role)
        permissions = []
        for method in permission_methods:
            if hasattr(test_user, method) and callable(getattr(test_user, method)):
                if getattr(test_user, method)():
                    # Convertir le nom de la méthode en texte lisible
                    permission_name = method.replace('can_', '').replace('_', ' ').title()
                    permissions.append(permission_name)
        role_permissions[role] = permissions
    
    # Calculer la moyenne des permissions
    total_permissions = sum(len(perms) for perms in role_permissions.values())
    average_permissions = total_permissions / len(UserRole) if UserRole else 0
    
    return render_template('admin/users/roles_new.html',
                         title='Gestion des Rôles',
                         roles=UserRole,
                         total_users=total_users,
                         most_used_role=most_used_role,
                         average_permissions=average_permissions,
                         role_permissions=role_permissions,
                         role_counts=role_counts,
                         max_users_count=max_users_count,
                         form=form)

@bp.route('/owner/business')
@login_required
def owner_business():
    """Informations de l'entreprise (vue owner)"""
    if not current_user.is_owner and not current_user.is_admin and not current_user.is_system_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    return render_template('admin/owner/business.html',
                         title='Informations Entreprise',
                         settings=settings)

@bp.route('/owner/settings')
@login_required
def owner_settings():
    """Paramètres owner"""
    if not current_user.is_owner and not current_user.is_system_admin and not current_user.is_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    return render_template('admin/settings.html',
                         title='Paramètres',
                         settings=settings)

@bp.route('/owner/settings/update', methods=['POST'])
@login_required
def update_settings():
    """Mettre à jour les paramètres"""
    if not current_user.is_owner:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('main.index'))
    
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    if not settings:
        settings = Settings(owner_id=current_user.id)
        db.session.add(settings)
    
    # Mise à jour des informations de l'entreprise
    settings.business_name = request.form.get('business_name')
    settings.business_phone = request.form.get('business_phone')
    settings.business_email = request.form.get('business_email')
    settings.business_address = request.form.get('business_address')
    
    # Mise à jour des paramètres système
    settings.timezone = request.form.get('timezone')
    settings.currency = request.form.get('currency')
    settings.date_format = request.form.get('date_format')
    settings.time_format = request.form.get('time_format')
    
    # Mise à jour des paramètres de notification
    settings.email_notifications = request.form.get('email_notifications') == 'on'
    settings.stock_alerts = request.form.get('stock_alerts') == 'on'
    
    try:
        db.session.commit()
        flash('Paramètres mis à jour avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour des paramètres: {str(e)}', 'error')
    
    return redirect(url_for('admin.owner_settings'))

