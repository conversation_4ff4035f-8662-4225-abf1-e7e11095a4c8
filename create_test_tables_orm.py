#!/usr/bin/env python3
"""
Script pour créer des tables de test en utilisant l'ORM Flask
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.tables.models_table import Table, TableStatus
from app.modules.auth.models import User

def create_test_tables():
    app = create_app()
    
    with app.app_context():
        try:
            # Récupérer le premier utilisateur
            user = User.query.first()
            if not user:
                print("Aucun utilisateur trouvé. Veuillez d'abord créer un utilisateur.")
                return False
            
            print(f"Création de tables pour l'utilisateur: {user.username}")
            
            # Supprimer les tables existantes pour cet utilisateur
            existing_tables = Table.query.filter_by(owner_id=user.id).all()
            for table in existing_tables:
                db.session.delete(table)
            
            # Créer des tables de test
            test_tables = [
                {"number": "1", "capacity": 2, "location": "Intérieur"},
                {"number": "2", "capacity": 4, "location": "Intérieur"},
                {"number": "3", "capacity": 6, "location": "Intérieur"},
                {"number": "4", "capacity": 2, "location": "Terrasse"},
                {"number": "5", "capacity": 4, "location": "Terrasse"},
                {"number": "6", "capacity": 8, "location": "Salle privée"},
                {"number": "7", "capacity": 2, "location": "Bar"},
                {"number": "8", "capacity": 4, "location": "Bar"},
            ]
            
            for table_data in test_tables:
                table = Table(
                    owner_id=user.id,
                    number=table_data["number"],
                    capacity=table_data["capacity"],
                    location=table_data["location"],
                    status=TableStatus.AVAILABLE
                )
                db.session.add(table)
            
            db.session.commit()
            print(f"✅ Créé {len(test_tables)} tables de test avec succès!")
            
            # Afficher les tables créées
            print("\nTables créées:")
            for table in Table.query.filter_by(owner_id=user.id).all():
                print(f"  - Table {table.number} ({table.location}) - {table.capacity} pers. - {table.status.value}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de la création des tables: {str(e)}")
            return False

if __name__ == "__main__":
    success = create_test_tables()
    if success:
        print("\n🎉 Tables de test créées avec succès!")
        print("Vous pouvez maintenant tester la sélection de table dans le POS.")
    else:
        print("\n💥 Échec de la création des tables de test.")
