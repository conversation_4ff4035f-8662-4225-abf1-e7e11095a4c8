{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gestion des Tables</h1>
        <div>
            <a href="{{ url_for('tables.reservations') }}" class="btn btn-info me-2">
                <i class="fas fa-calendar-alt"></i> Réservations
            </a>
            <a href="{{ url_for('tables.new') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle Table
            </a>
        </div>
    </div>

    <div class="row">
        {% for table in tables %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Table {{ table.number }}</h5>
                        <span class="badge {% if table.status == 'available' %}bg-success{% elif table.status == 'occupied' %}bg-danger{% elif table.status == 'reserved' %}bg-warning{% else %}bg-secondary{% endif %}">
                            {% if table.status == 'available' %}
                                Disponible
                            {% elif table.status == 'occupied' %}
                                Occupée
                            {% elif table.status == 'reserved' %}
                                Réservée
                            {% else %}
                                Nettoyage
                            {% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Capacité:</strong> {{ table.capacity }} personnes<br>
                        <strong>Emplacement:</strong> 
                        {% if table.location == 'interior' %}
                            Intérieur
                        {% elif table.location == 'terrace' %}
                            Terrasse
                        {% elif table.location == 'bar' %}
                            Bar
                        {% else %}
                            Salle privée
                        {% endif %}
                    </div>

                    {% if table.current_order_id %}
                    <div class="alert alert-info mb-3">
                        <strong>Commande en cours:</strong> #{{ table.current_order_id }}
                    </div>
                    {% endif %}

                    {% if table.reservations %}
                    <div class="mb-3">
                        <strong>Prochaine réservation:</strong><br>
                        {% set next_reservation = table.reservations|selectattr('is_active')|first %}
                        {% if next_reservation %}
                            {{ next_reservation.customer_name }}<br>
                            {{ next_reservation.reservation_date.strftime('%d/%m/%Y %H:%M') }}
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <a href="{{ url_for('tables.show', id=table.id) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('tables.edit', id=table.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete({{ table.id }})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer cette table ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(tableId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/tables/${tableId}/delete`;
    modal.show();
}
</script>
{% endblock %} 