from datetime import datetime, timedelta
from app import db

class TableStatus:
    AVAILABLE = 'available'
    OCCUPIED = 'occupied'
    RESERVED = 'reserved'
    CLEANING = 'cleaning'

class Table(db.Model):
    __tablename__ = 'tables'
    __table_args__ = (
        db.UniqueConstraint('owner_id', 'number', name='uq_owner_table_number'),
        {'extend_existing': True}
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.<PERSON>teger, db.ForeignKey('users.id'), nullable=False)
    number = db.Column(db.String(10), nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default=TableStatus.AVAILABLE)
    location = db.Column(db.String(50), nullable=True)  # e.g., "Terrasse", "Intérieur", etc.
    current_order_id = db.Column(db.<PERSON>, db.<PERSON><PERSON>('sales.id'), nullable=True)

    # Relations
    owner = db.relationship('User', backref='tables', lazy=True)
    orders = db.relationship('Sale', backref='table', lazy=True)
    reservations = db.relationship('TableReservation', backref='table', lazy=True)

    def __repr__(self):
        return f'<Table {self.number}>'

    def is_available(self):
        return self.status == TableStatus.AVAILABLE

    def occupy(self, order_id, commit=True):
        if self.is_available():
            self.status = TableStatus.OCCUPIED
            self.current_order_id = order_id
            if commit:
                db.session.commit()
            return True
        return False

    def release(self):
        self.status = TableStatus.CLEANING
        self.current_order_id = None
        db.session.commit()

    def mark_clean(self):
        if self.status == TableStatus.CLEANING:
            self.status = TableStatus.AVAILABLE
            db.session.commit()
            return True
        return False

    def reset_table(self):
        """Remet la table à l'état disponible après une commande"""
        self.status = TableStatus.AVAILABLE
        self.current_order_id = None
        # Note: db.session.commit() sera appelé par la fonction appelante

class TableReservation(db.Model):
    __tablename__ = 'table_reservations'

    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('tables.id'), nullable=False)
    customer_name = db.Column(db.String(100), nullable=False)
    customer_phone = db.Column(db.String(20), nullable=False)
    number_of_guests = db.Column(db.Integer, nullable=False)
    reservation_date = db.Column(db.DateTime, nullable=False)
    duration_minutes = db.Column(db.Integer, default=120)  # Durée par défaut 2h
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Reservation {self.customer_name} - Table {self.table_id}>'

    def is_active(self):
        now = datetime.utcnow()
        return self.reservation_date <= now <= self.reservation_date + timedelta(minutes=self.duration_minutes)