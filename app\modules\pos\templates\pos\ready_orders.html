{% extends "base.html" %}

{% block title %}Commandes Prêtes{% endblock %}

{% block extra_css %}
<style>
/* Styles pour la page des commandes prêtes */
.ready-orders-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 20px 0;
    margin: -20px -15px 30px -15px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.ready-orders-header h2 {
    margin: 0;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.order-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 25px;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.order-card-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border: none;
    position: relative;
}

.order-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.order-number {
    font-size: 1.1em;
    font-weight: 600;
    margin: 0;
}

.order-time {
    font-size: 0.9em;
    opacity: 0.9;
    margin: 0;
}

.table-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3);
}

.items-list {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.item-row:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 500;
    color: #495057;
}

.item-quantity {
    background: #007bff;
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.kitchen-note {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.kitchen-note-title {
    color: #856404;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.kitchen-note-text {
    color: #856404;
    margin: 0;
    font-style: italic;
}

.total-section {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.total-amount {
    font-size: 1.5em;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons {
    display: grid;
    gap: 10px;
    padding: 20px;
    background: #f8f9fa;
}

.btn-pay-cash {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-pay-cash:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-pay-other {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-pay-other:hover {
    background: linear-gradient(135deg, #e0a800, #e8690b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    color: white;
}

.btn-delivered {
    background: linear-gradient(135deg, #6c757d, #495057);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-delivered:hover {
    background: linear-gradient(135deg, #5a6268, #3d4142);
    transform: translateY(-2px);
    color: white;
}

.nav-buttons {
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.nav-btn-kitchen {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    border: none;
}

.nav-btn-kitchen:hover {
    background: linear-gradient(135deg, #e0a800, #e8690b);
    transform: translateY(-2px);
    color: white;
}

.nav-btn-pos {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
    border: none;
}

.nav-btn-pos:hover {
    background: linear-gradient(135deg, #0056b3, #520dc2);
    transform: translateY(-2px);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state-icon {
    font-size: 4em;
    color: #17a2b8;
    margin-bottom: 20px;
}

.empty-state-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.empty-state-text {
    color: #6c757d;
    margin-bottom: 30px;
}

/* Animation pour les nouvelles commandes */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideInUp 0.5s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .ready-orders-header {
        margin: -20px -15px 20px -15px;
        padding: 15px 0;
    }

    .nav-buttons {
        flex-direction: column;
    }

    .order-card {
        margin-bottom: 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête amélioré -->
    <div class="ready-orders-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-bell me-2"></i>
                    Commandes Prêtes à Servir
                    {% if orders %}
                        <span class="badge bg-light text-dark ms-2">{{ orders|length }}</span>
                    {% endif %}
                </h2>
                <div class="nav-buttons">
                    <a href="{{ url_for('pos.kitchen_orders') }}" class="nav-btn nav-btn-kitchen">
                        <i class="fas fa-utensils"></i> Cuisine
                    </a>
                    <a href="{{ url_for('pos.index') }}" class="nav-btn nav-btn-pos">
                        <i class="fas fa-cash-register"></i> POS
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if orders %}
    <div class="row">
        {% for order in orders %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card order-card h-100">
                <!-- En-tête de la commande -->
                <div class="order-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="order-number">
                            <i class="fas fa-receipt me-2"></i>
                            Commande #{{ order.reference }}
                        </h6>
                        <small class="order-time">
                            <i class="fas fa-clock me-1"></i>
                            {{ order.created_at.strftime('%H:%M') }}
                        </small>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Informations de table -->
                    {% if order.table %}
                    <div class="table-info">
                        <i class="fas fa-table me-2"></i>
                        <strong>Table {{ order.table.number }}</strong>
                        {% if order.table.location %}
                            <small class="d-block mt-1">{{ order.table.location }}</small>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Articles de la commande -->
                    <div class="items-list">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-list me-2"></i>Articles ({{ order.items|length }})
                        </h6>
                        {% for item in order.items %}
                        <div class="item-row">
                            <span class="item-name">{{ item.product.name }}</span>
                            <span class="item-quantity">{{ item.quantity|int }}</span>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Note de cuisine -->
                    {% if order.kitchen_note %}
                    <div class="kitchen-note">
                        <div class="kitchen-note-title">
                            <i class="fas fa-sticky-note"></i>
                            Note pour la cuisine
                        </div>
                        <p class="kitchen-note-text">{{ order.kitchen_note }}</p>
                    </div>
                    {% endif %}

                    <!-- Total -->
                    <div class="total-section">
                        <p class="total-amount">
                            <i class="fas fa-euro-sign me-2"></i>{{ "%.2f"|format(order.total) }} €
                        </p>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <!-- Formulaire de paiement direct -->
                    <form action="{{ url_for('pos.process_payment') }}" method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="sale_id" value="{{ order.id }}">
                        <input type="hidden" name="payment_method" value="CASH">
                        <input type="hidden" name="amount_tendered" value="{{ order.total }}">
                        <button type="submit" class="btn btn-pay-cash w-100">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Encaisser en Espèces
                        </button>
                    </form>

                    <button type="button" class="btn btn-pay-other w-100" onclick="processPayment({{ order.id }})">
                        <i class="fas fa-credit-card me-2"></i>
                        Autre Méthode de Paiement
                    </button>

                    <button type="button" class="btn btn-delivered w-100" onclick="markAsDelivered({{ order.id }})">
                        <i class="fas fa-check-circle me-2"></i>
                        Marquer comme Servi
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- État vide amélioré -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-bell-slash"></i>
        </div>
        <h4 class="empty-state-title">Aucune commande prête</h4>
        <p class="empty-state-text">
            Toutes les commandes ont été servies ou sont encore en préparation en cuisine.
        </p>
        <div class="d-flex justify-content-center gap-3">
            <a href="{{ url_for('pos.kitchen_orders') }}" class="nav-btn nav-btn-kitchen">
                <i class="fas fa-utensils"></i> Voir la Cuisine
            </a>
            <a href="{{ url_for('pos.index') }}" class="nav-btn nav-btn-pos">
                <i class="fas fa-cash-register"></i> Retour au POS
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal de paiement amélioré -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 15px 15px 0 0;">
                <h5 class="modal-title" id="paymentModalLabel">
                    <i class="fas fa-cash-register me-2"></i>
                    Encaisser la Commande
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- Résumé de la commande -->
                <div class="payment-summary mb-4" style="background: #f8f9fa; border-radius: 10px; padding: 15px;">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-receipt me-2"></i>Résumé
                    </h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Commande #<span id="orderReference"></span></span>
                        <strong class="text-success" style="font-size: 1.2em;" id="orderTotal"></strong>
                    </div>
                </div>

                <form id="paymentForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" id="saleId" name="sale_id">

                    <div class="mb-4">
                        <label for="paymentMethod" class="form-label fw-bold">
                            <i class="fas fa-credit-card me-2"></i>Méthode de paiement
                        </label>
                        <select class="form-select form-select-lg" id="paymentMethod" name="payment_method" required style="border-radius: 10px;">
                            <option value="CASH">💵 Espèces</option>
                            <option value="CARD">💳 Carte Bancaire</option>
                            <option value="CHECK">📝 Chèque</option>
                        </select>
                    </div>

                    <div class="mb-3" id="amountTenderedGroup">
                        <label for="amountTendered" class="form-label fw-bold">
                            <i class="fas fa-money-bill-wave me-2"></i>Montant reçu
                        </label>
                        <div class="input-group input-group-lg">
                            <input type="number" class="form-control" id="amountTendered" name="amount_tendered"
                                   step="0.01" min="0" style="border-radius: 10px 0 0 10px;">
                            <span class="input-group-text" style="border-radius: 0 10px 10px 0; background: #28a745; color: white; border-color: #28a745;">€</span>
                        </div>
                        <div id="changeAmount" class="mt-2" style="display: none;">
                            <small class="text-muted">Monnaie à rendre: </small>
                            <strong class="text-warning" id="changeValue">0.00 €</strong>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer p-4" style="border-top: 1px solid #e9ecef;">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal" style="border-radius: 10px;">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-success btn-lg" onclick="submitPayment()" style="border-radius: 10px; background: linear-gradient(135deg, #28a745, #20c997); border: none;">
                    <i class="fas fa-check me-2"></i>Confirmer le Paiement
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentOrderTotal = 0;
let currentOrderReference = '';

function processPayment(orderId) {
    // Récupérer les détails de la commande
    const orderCard = document.querySelector(`button[onclick="processPayment(${orderId})"]`).closest('.card');
    const totalText = orderCard.querySelector('.total-amount').textContent;
    const referenceText = orderCard.querySelector('.order-number').textContent;

    // Extraire les valeurs
    currentOrderTotal = parseFloat(totalText.match(/[\d.]+/)[0]);
    currentOrderReference = referenceText.replace('Commande #', '').trim();

    // Remplir le modal
    document.getElementById('saleId').value = orderId;
    document.getElementById('amountTendered').value = currentOrderTotal;
    document.getElementById('orderReference').textContent = currentOrderReference;
    document.getElementById('orderTotal').textContent = currentOrderTotal.toFixed(2) + ' €';

    // Réinitialiser le formulaire
    document.getElementById('paymentMethod').value = 'CASH';
    document.getElementById('amountTenderedGroup').style.display = 'block';
    document.getElementById('changeAmount').style.display = 'none';

    // Afficher le modal avec animation
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();

    // Focus sur le champ montant
    setTimeout(() => {
        document.getElementById('amountTendered').focus();
        document.getElementById('amountTendered').select();
    }, 300);
}

function submitPayment() {
    const form = document.getElementById('paymentForm');
    const formData = new FormData(form);
    const submitButton = document.querySelector('button[onclick="submitPayment()"]');

    // Désactiver le bouton pour éviter les doubles clics
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';

    // Convertir FormData en objet JSON
    const data = {
        sale_id: formData.get('sale_id'),
        payment_method: formData.get('payment_method'),
        amount_tendered: parseFloat(formData.get('amount_tendered')) || currentOrderTotal,
        csrf_token: formData.get('csrf_token')
    };

    // Vérifier le token CSRF
    const csrfToken = document.querySelector('meta[name="csrf-token"]');

    fetch('/pos/process_ready_payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken ? csrfToken.content : ''
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
            if (modal) modal.hide();

            // Afficher une notification de succès moderne
            showSuccessNotification(data.change);

            // Recharger la page après un délai
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showErrorNotification(data.error);
            // Réactiver le bouton
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-check me-2"></i>Confirmer le Paiement';
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        showErrorNotification('Erreur de connexion: ' + error.message);
        // Réactiver le bouton
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-check me-2"></i>Confirmer le Paiement';
    });
}

function showSuccessNotification(change) {
    const notification = document.createElement('div');
    notification.className = 'position-fixed top-0 start-50 translate-middle-x mt-3';
    notification.style.zIndex = '9999';

    let message = 'Paiement traité avec succès !';
    if (change > 0) {
        message += `<br><strong>Monnaie à rendre : ${change.toFixed(2)} €</strong>`;
    }

    notification.innerHTML = `
        <div class="alert alert-success alert-dismissible fade show shadow-lg" style="border-radius: 15px; border: none;">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                <div>
                    <h6 class="mb-1">Succès !</h6>
                    <div>${message}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function showErrorNotification(error) {
    const notification = document.createElement('div');
    notification.className = 'position-fixed top-0 start-50 translate-middle-x mt-3';
    notification.style.zIndex = '9999';

    notification.innerHTML = `
        <div class="alert alert-danger alert-dismissible fade show shadow-lg" style="border-radius: 15px; border: none;">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger me-3"></i>
                <div>
                    <h6 class="mb-1">Erreur !</h6>
                    <div>${error}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Gestion de l'affichage du champ "Montant reçu" et calcul de la monnaie
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const amountTenderedGroup = document.getElementById('amountTenderedGroup');
    const amountTenderedInput = document.getElementById('amountTendered');
    const changeAmountDiv = document.getElementById('changeAmount');
    const changeValueSpan = document.getElementById('changeValue');

    // Gestion du changement de méthode de paiement
    paymentMethodSelect.addEventListener('change', function() {
        if (this.value === 'CASH') {
            amountTenderedGroup.style.display = 'block';
            amountTenderedInput.required = true;
            calculateChange();
        } else {
            amountTenderedGroup.style.display = 'none';
            amountTenderedInput.required = false;
            amountTenderedInput.value = currentOrderTotal;
            changeAmountDiv.style.display = 'none';
        }
    });

    // Calcul de la monnaie en temps réel
    amountTenderedInput.addEventListener('input', calculateChange);

    function calculateChange() {
        const amountTendered = parseFloat(amountTenderedInput.value) || 0;
        const change = amountTendered - currentOrderTotal;

        if (change >= 0 && amountTendered > 0) {
            changeValueSpan.textContent = change.toFixed(2) + ' €';
            changeAmountDiv.style.display = 'block';

            if (change > 0) {
                changeValueSpan.className = 'text-warning fw-bold';
            } else {
                changeValueSpan.className = 'text-success fw-bold';
            }
        } else {
            changeAmountDiv.style.display = 'none';
        }
    }
});

// Amélioration de la fonction markAsDelivered
function markAsDelivered(orderId) {
    // Utiliser une confirmation moderne
    if (confirm('🍽️ Marquer cette commande comme servie ?\n\nCette action indique que la commande a été livrée au client.')) {
        const button = document.querySelector(`button[onclick="markAsDelivered(${orderId})"]`);
        const originalText = button.innerHTML;

        // Désactiver le bouton et afficher un spinner
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';

        fetch(`/pos/kitchen/order/${orderId}/delivered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Animation de succès
                button.innerHTML = '<i class="fas fa-check me-2"></i>Servi !';
                button.className = 'btn btn-success w-100';

                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showErrorNotification(data.error);
                button.disabled = false;
                button.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorNotification('Erreur de connexion');
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}

// Auto-refresh avec indicateur visuel
let refreshInterval;
let refreshCountdown = 30;

function startAutoRefresh() {
    refreshInterval = setInterval(function() {
        refreshCountdown--;

        // Afficher le countdown dans le titre de la page
        if (refreshCountdown > 0) {
            document.title = `Commandes Prêtes (${refreshCountdown}s)`;
        } else {
            document.title = 'Commandes Prêtes - Actualisation...';
            location.reload();
        }
    }, 1000);
}

// Démarrer l'auto-refresh
startAutoRefresh();

// Arrêter l'auto-refresh si l'utilisateur interagit avec la page
document.addEventListener('click', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        document.title = 'Commandes Prêtes';

        // Redémarrer après 5 secondes d'inactivité
        setTimeout(() => {
            refreshCountdown = 30;
            startAutoRefresh();
        }, 5000);
    }
});

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    // F5 ou Ctrl+R pour actualiser
    if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
        e.preventDefault();
        location.reload();
    }

    // Échap pour fermer le modal
    if (e.key === 'Escape') {
        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        if (modal) modal.hide();
    }
});
</script>
{% endblock %}
