{% extends "base.html" %}

{% block title %}Commandes Cuisine{% endblock %}

{% block content %}

<div class="container mt-4">
    <h2 class="mb-4">
        <i class="fas fa-utensils"></i> Commandes en cours
    </h2>

    <div class="row">
        {% for order in orders %}
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        Commande #{{ order.id }}
                        <small class="text-muted float-end">
                            {{ order.created_at.strftime('%H:%M') }}
                        </small>
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for item in order.items %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {{ item.product.name }}
                            <span class="badge bg-primary rounded-pill">{{ item.quantity }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-footer">
                    <button class="btn btn-success btn-sm w-100" onclick="markAsReady({{ order.id }})">
                        <i class="fas fa-check"></i> Prêt
                    </button>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Aucune commande en attente.
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% block scripts %}
<script>
function markAsReady(orderId) {
    if (confirm('Marquer cette commande comme prête ?')) {
        fetch(`/pos/kitchen/order/${orderId}/ready`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Recharger la page pour mettre à jour la liste
                location.reload();
            } else {
                alert('Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }
}

// Rafraîchir la page toutes les 30 secondes
setInterval(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
{% endblock %} 