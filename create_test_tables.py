#!/usr/bin/env python3
"""
Script pour créer des tables de test
"""

from app import create_app, db
from app.modules.tables.models_table import Table, TableStatus
from app.modules.auth.models import User

def create_test_tables():
    app = create_app()
    
    with app.app_context():
        # Récupérer le premier utilisateur (ou créer un utilisateur de test)
        user = User.query.first()
        if not user:
            print("Aucun utilisateur trouvé. Veuillez d'abord créer un utilisateur.")
            return
        
        # Supprimer les tables existantes pour cet utilisateur
        Table.query.filter_by(owner_id=user.id).delete()
        
        # Créer des tables de test
        test_tables = [
            {"number": "1", "capacity": 2, "location": "Intérieur"},
            {"number": "2", "capacity": 4, "location": "Intérieur"},
            {"number": "3", "capacity": 6, "location": "Intérieur"},
            {"number": "4", "capacity": 2, "location": "Terrasse"},
            {"number": "5", "capacity": 4, "location": "Terrasse"},
            {"number": "6", "capacity": 8, "location": "Salle privée"},
            {"number": "7", "capacity": 2, "location": "Bar"},
            {"number": "8", "capacity": 4, "location": "Bar"},
        ]
        
        for table_data in test_tables:
            table = Table(
                owner_id=user.id,
                number=table_data["number"],
                capacity=table_data["capacity"],
                location=table_data["location"],
                status=TableStatus.AVAILABLE
            )
            db.session.add(table)
        
        db.session.commit()
        print(f"Créé {len(test_tables)} tables de test pour l'utilisateur {user.username}")

if __name__ == "__main__":
    create_test_tables()
