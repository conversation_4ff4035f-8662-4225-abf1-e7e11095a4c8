from app import db
from datetime import datetime
import enum
from sqlalchemy.types import TypeDecorator, String
import os
from werkzeug.utils import secure_filename
from flask import current_app
from flask_login import AnonymousUserMixin

# Anonymous User for Flask-Login - moved from models.py
class Anonymous(AnonymousUserMixin):
    @property
    def is_system_admin(self):
        return False
    
    @property
    def is_owner(self):
        return False
    
    @property
    def is_admin(self):
        return False
    
    @property
    def is_manager(self):
        return False
    
    def can_manage_cash_register(self):
        return False
        
    def can_access_cash_register(self):
        return False
        
    def has_permission(self, permission):
        return False
        
    def can_access_reports(self):
        return False
        
    def can_manage_inventory(self):
        return False
        
    def can_process_sales(self):
        return False
        
    def can_access_kitchen(self):
        return False
        
    def can_manage_customers(self):
        return False
        
    def can_manage_promotions(self):
        return False
        
    def can_manage_tables(self):
        return False
        
    def can_manage_expenses(self):
        return False

class CashRegisterOperationType(enum.Enum):
    OPENING = "OPENING"
    CLOSING = "CLOSING"
    CASH_IN = "CASH_IN"
    CASH_OUT = "CASH_OUT"
    SALE = "SALE"
    BANK_DEPOSIT = "BANK_DEPOSIT"

class CashOutReason(str, enum.Enum):
    SUPPLIER_PAYMENT = "supplier_payment"
    PRODUCT_PURCHASE = "product_purchase"
    INGREDIENT_PURCHASE = "ingredient_purchase"
    EXPENSE = "expense"
    OTHER = "other"

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value)
        except ValueError:
            return cls.OTHER

class CashOutReasonType(TypeDecorator):
    impl = String
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        return value.value

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return CashOutReason.from_str(value)

class PaymentMethod(enum.Enum):
    CASH = "CASH"
    CARD = "CARD"
    CHECK = "CHECK"
    OTHER = "OTHER"

    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value.upper())
        except ValueError:
            return cls.OTHER

    def __eq__(self, other):
        if isinstance(other, str):
            return self.value.upper() == other.upper()
        return super().__eq__(other)

class ReceiptType(enum.Enum):
    BANK_DEPOSIT = "bank_deposit"
    SUPPLIER_PAYMENT = "supplier_payment"
    OTHER = "other"

class CashRegister(db.Model):
    __tablename__ = 'cash_register'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    current_balance = db.Column(db.Float, default=0)
    is_open = db.Column(db.Boolean, default=False)
    float_amount = db.Column(db.Float, default=0)
    last_opened_at = db.Column(db.DateTime)
    last_closed_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    operations = db.relationship('CashOperation', backref='register', lazy='dynamic')
    owner = db.relationship('User', backref='cash_registers')

    def open(self, user_id, initial_amount=None):
        if self.is_open:
            return False, "La caisse est déjà ouverte"
        
        if initial_amount is not None:
            if initial_amount < self.float_amount:
                return False, f"Le montant initial doit être au moins égal au fond de caisse ({self.float_amount}€)"
            self.current_balance = initial_amount
        
        self.is_open = True
        self.last_opened_at = datetime.utcnow()
        
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.OPENING,
            amount=self.current_balance,
            initial_amount=self.current_balance,
            final_amount=None,
            user_id=user_id,
            owner_id=self.owner_id,
            note=f"Ouverture avec {self.current_balance}€"
        )
        db.session.add(operation)
        db.session.commit()
        
        return True, "Caisse ouverte avec succès"

    def close(self, user_id, final_amount=None):
        if not self.is_open:
            return False, "La caisse est déjà fermée"
        
        if final_amount is not None:
            if abs(final_amount - self.current_balance) > 0.01:
                return False, f"Le montant final ({final_amount}) ne correspond pas au solde actuel ({self.current_balance})"
        
        # Trouver l'opération d'ouverture correspondante
        opening_operation = CashOperation.query.filter_by(
            register_id=self.id,
            type=CashRegisterOperationType.OPENING
        ).order_by(CashOperation.created_at.desc()).first()
        
        self.is_open = False
        self.last_closed_at = datetime.utcnow()
        
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.CLOSING,
            amount=self.current_balance,
            initial_amount=opening_operation.initial_amount if opening_operation else None,
            final_amount=self.current_balance,
            user_id=user_id,
            owner_id=self.owner_id,
            note=f"Fermeture avec {self.current_balance}€"
        )
        db.session.add(operation)
        
        # Si un fond de caisse est défini, calculer le montant à transférer à la trésorerie
        if self.float_amount > 0:
            amount_to_treasury = max(0, self.current_balance - self.float_amount)
            self.current_balance = self.float_amount
        else:
            amount_to_treasury = self.current_balance
            self.current_balance = 0
            
        db.session.commit()
        
        return True, f"Caisse fermée avec succès. Montant transféré à la trésorerie: {amount_to_treasury}"

    def add_cash(self, amount, source, note, user_id):
        if not self.is_open:
            return False, "La caisse est fermée"
        
        self.current_balance += amount
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.CASH_IN,
            amount=amount,
            note=note,
            source=source,
            user_id=user_id,
            owner_id=self.owner_id
        )
        db.session.add(operation)
        db.session.commit()
        
        return True, "Entrée de caisse enregistrée"

    def remove_cash(self, amount, reason, note, user_id, linked_objects=None):
        if not self.is_open:
            return False, "La caisse est fermée"
            
        if amount > self.current_balance:
            return False, "Solde insuffisant"
        
        self.current_balance -= amount
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.CASH_OUT,
            amount=-amount,  # Montant négatif pour une sortie
            reason=CashOutReason.from_str(reason),  # Utiliser la méthode from_str pour convertir
            note=note,
            user_id=user_id,
            owner_id=self.owner_id
        )
        
        if linked_objects:
            operation.linked_objects = linked_objects
            
        db.session.add(operation)
        db.session.commit()
        
        return True, "Sortie de caisse enregistrée"

    def add_sale(self, amount, sale_id, user_id, payment_method=None):
        """Ajoute une vente à la caisse"""
        if not self.is_open:
            return False, "La caisse est fermée"
        
        # N'ajouter au solde que les paiements en espèces
        if payment_method == PaymentMethod.CASH:
            self.current_balance += amount
        
        try:
            operation = CashOperation(
                register_id=self.id,
                type=CashRegisterOperationType.SALE,
                amount=amount,
                payment_method=payment_method,
                note=f"Vente #{sale_id}",
                user_id=user_id,
                owner_id=self.owner_id
            )
            db.session.add(operation)
            db.session.commit()
            
            return True, "Vente enregistrée"
        except Exception as e:
            db.session.rollback()
            return False, f"Erreur lors de l'enregistrement de la vente: {str(e)}"

    def get_sales_summary(self):
        """Calcule les totaux des ventes par mode de paiement depuis l'ouverture"""
        if not self.last_opened_at:
            return {
                'total': 0,
                'cash': 0,
                'card': 0,
                'check': 0,
                'other': 0
            }
            
        totals = {
            'total': 0,
            'cash': 0,
            'card': 0,
            'check': 0,
            'other': 0
        }
        
        operations = self.operations.filter(
            CashOperation.created_at >= self.last_opened_at,
            CashOperation.type == CashRegisterOperationType.SALE
        ).all()
        
        for op in operations:
            totals['total'] += op.amount
            
            if op.payment_method == PaymentMethod.CASH:
                totals['cash'] += op.amount
            elif op.payment_method == PaymentMethod.CARD:
                totals['card'] += op.amount
            elif op.payment_method == PaymentMethod.CHECK:
                totals['check'] += op.amount
            else:
                totals['other'] += op.amount
                
        return totals
        
    def get_movements_summary(self):
        """Calcule les totaux des mouvements de caisse depuis l'ouverture"""
        if not self.last_opened_at:
            return {
                'cash_in': 0,
                'cash_out': 0
            }
            
        totals = {
            'cash_in': 0,
            'cash_out': 0
        }
        
        # Entrées
        cash_in_operations = self.operations.filter(
            CashOperation.created_at >= self.last_opened_at,
            CashOperation.type == CashRegisterOperationType.CASH_IN
        ).all()
        
        for op in cash_in_operations:
            totals['cash_in'] += op.amount
            
        # Sorties
        cash_out_operations = self.operations.filter(
            CashOperation.created_at >= self.last_opened_at,
            CashOperation.type == CashRegisterOperationType.CASH_OUT
        ).all()
        
        for op in cash_out_operations:
            totals['cash_out'] += abs(op.amount)  # Convertir en valeur positive
            
        return totals
        
    def get_total_balance(self):
        """Calcule le solde total de la caisse"""
        # Si le solde est déjà défini, l'utiliser
        if self.current_balance is not None:
            return self.current_balance
            
        # Sinon, calculer le solde à partir des opérations
        balance = 0
        for op in self.operations:
            if op.type == CashRegisterOperationType.OPENING:
                balance += op.amount
            elif op.type == CashRegisterOperationType.CASH_IN:
                balance += op.amount
            elif op.type == CashRegisterOperationType.CASH_OUT:
                balance += op.amount  # Déjà négatif normalement
            elif op.type == CashRegisterOperationType.SALE and op.payment_method == PaymentMethod.CASH:
                balance += op.amount
                
        return balance
        
    def reset_balance(self, user_id):
        """Réinitialise le solde de la caisse (conserve le fond de caisse)"""
        if not self.is_open:
            return False, "La caisse est fermée"
            
        # Calculer le montant à retirer (solde actuel - fond de caisse)
        amount_to_remove = self.current_balance - self.float_amount
        
        if amount_to_remove <= 0:
            return False, "Le solde actuel est inférieur ou égal au fond de caisse, impossible de le réinitialiser"
            
        # Créer une opération de sortie pour le montant à retirer
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.CASH_OUT,
            amount=-amount_to_remove,
            note="Réinitialisation du solde au fond de caisse",
            user_id=user_id,
            owner_id=self.owner_id
        )
        
        # Mettre à jour le solde
        self.current_balance = self.float_amount
        
        db.session.add(operation)
        db.session.commit()
        
        return True, f"Solde réinitialisé au fond de caisse ({self.float_amount}€)"
        
    def close_and_transfer_to_treasury(self, user_id, final_amount=None, note=None):
        """Ferme la caisse et transfère le solde à la trésorerie"""
        if not self.is_open:
            return False, "La caisse est déjà fermée"
            
        if final_amount is not None and abs(final_amount - self.current_balance) > 0.01:
            return False, f"Le montant final ({final_amount}) ne correspond pas au solde actuel ({self.current_balance})"
            
        # Trouver l'opération d'ouverture correspondante
        opening_operation = CashOperation.query.filter_by(
            register_id=self.id,
            type=CashRegisterOperationType.OPENING
        ).order_by(CashOperation.created_at.desc()).first()
        
        self.is_open = False
        self.last_closed_at = datetime.utcnow()
        
        # Calculer le montant à transférer (solde actuel - fond de caisse)
        amount_to_treasury = max(0, self.current_balance - self.float_amount)
        
        # Créer une opération de fermeture
        operation = CashOperation(
            register_id=self.id,
            type=CashRegisterOperationType.CLOSING,
            amount=self.current_balance,
            initial_amount=opening_operation.initial_amount if opening_operation else None,
            final_amount=self.current_balance,
            user_id=user_id,
            owner_id=self.owner_id,
            note=note or f"Fermeture avec {self.current_balance}€ (transfert de {amount_to_treasury}€ à la trésorerie)"
        )
        
        # Mettre à jour le solde (conserver uniquement le fond de caisse)
        self.current_balance = self.float_amount
        
        db.session.add(operation)
        db.session.commit()
        
        return True, f"Caisse fermée avec succès. Montant transféré à la trésorerie: {amount_to_treasury}€"
        
    @classmethod
    def get_current(cls, owner_id):
        """Récupère la caisse actuelle pour un propriétaire donné"""
        return cls.query.filter_by(owner_id=owner_id).first()
        
    @classmethod
    def get_open_register(cls, owner_id):
        """Récupère la caisse ouverte pour un propriétaire donné"""
        return cls.query.filter_by(owner_id=owner_id, is_open=True).first()

class PaymentMethodType(TypeDecorator):
    impl = String
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        if isinstance(value, PaymentMethod):
            return value.value
        return value

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return PaymentMethod.from_str(value)

class CashOperation(db.Model):
    __tablename__ = 'cash_operations'
    
    id = db.Column(db.Integer, primary_key=True)
    register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    type = db.Column(db.Enum(CashRegisterOperationType), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    initial_amount = db.Column(db.Float)
    final_amount = db.Column(db.Float)
    reason = db.Column(db.String(50))
    source = db.Column(db.String(50))
    payment_method = db.Column(PaymentMethodType)
    note = db.Column(db.Text)
    linked_objects = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    user = db.relationship('User', foreign_keys=[user_id], backref='cash_operations_made')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='cash_operations_owned')
    
    @property
    def created_by(self):
        """Retourne le nom de l'utilisateur qui a créé l'opération"""
        return self.user.username if self.user else "Système"
    
    def __repr__(self):
        return f'<CashOperation {self.id} {self.type} {self.amount}>'
        
    def get_linked_objects_details(self):
        """Récupère les détails des objets liés à cette opération"""
        if not self.linked_objects:
            return None
            
        details = {}
        
        # Traiter les différents types d'objets liés
        if "supplier_id" in self.linked_objects:
            from app.modules.inventory.models_supplier import Supplier
            supplier_id = self.linked_objects["supplier_id"]
            supplier = Supplier.query.get(supplier_id)
            if supplier:
                details["supplier"] = supplier.name
                
        if "product_id" in self.linked_objects:
            from app.modules.inventory.models_product import Product
            product_id = self.linked_objects["product_id"]
            product = Product.query.get(product_id)
            if product:
                details["product"] = product.name
                
        if "ingredient_id" in self.linked_objects:
            from app.modules.inventory.models_recipe import Ingredient
            ingredient_id = self.linked_objects["ingredient_id"]
            ingredient = Ingredient.query.get(ingredient_id)
            if ingredient:
                details["ingredient"] = ingredient.name
                
        if "expense_id" in self.linked_objects:
            from app.modules.expenses.models_expense import Expense
            expense_id = self.linked_objects["expense_id"]
            expense = Expense.query.get(expense_id)
            if expense:
                details["expense"] = expense.description
                
        return details
        
    def add_receipt(self, file, receipt_type=ReceiptType.OTHER):
        """Ajoute un reçu à l'opération"""
        return Receipt.save_receipt(file, self.id, receipt_type)

class Receipt(db.Model):
    __tablename__ = 'receipts'
    
    id = db.Column(db.Integer, primary_key=True)
    operation_id = db.Column(db.Integer, db.ForeignKey('cash_operations.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    receipt_type = db.Column(db.Enum(ReceiptType), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @staticmethod
    def save_receipt(file, operation_id, receipt_type):
        """Enregistre un fichier de reçu sur le serveur et crée une entrée dans la base de données"""
        if not file:
            return False, "Aucun fichier fourni"
            
        # Sécuriser le nom du fichier
        original_filename = secure_filename(file.filename)
        
        # Créer un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"receipt_{operation_id}_{timestamp}_{original_filename}"
        
        # Créer le dossier des reçus s'il n'existe pas
        receipts_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'receipts')
        os.makedirs(receipts_dir, exist_ok=True)
        
        # Chemin complet pour sauvegarder le fichier
        filepath = os.path.join(receipts_dir, filename)
        
        try:
            file.save(filepath)
            
            # Créer l'entrée dans la base de données
            receipt = Receipt(
                operation_id=operation_id,
                filename=filename,
                original_filename=original_filename,
                receipt_type=receipt_type
            )
            db.session.add(receipt)
            db.session.commit()
            
            return True, "Reçu enregistré avec succès"
        except Exception as e:
            db.session.rollback()
            # Supprimer le fichier s'il a été créé
            if os.path.exists(filepath):
                os.remove(filepath)
            return False, f"Erreur lors de l'enregistrement du reçu: {str(e)}"

class CashRegisterSettings(db.Model):
    __tablename__ = 'cash_register_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    minimum_amount_required = db.Column(db.Boolean, default=False)
    minimum_amount = db.Column(db.Float, default=0.01)
    use_last_closing_amount = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    owner = db.relationship('User', backref='cash_register_settings')
    
    @staticmethod
    def get_settings(owner_id):
        """Récupère les paramètres de caisse pour un propriétaire donné"""
        settings = CashRegisterSettings.query.filter_by(owner_id=owner_id).first()
        if not settings:
            settings = CashRegisterSettings(owner_id=owner_id)
            db.session.add(settings)
            db.session.commit()
        return settings