from flask import render_template, redirect, url_for, jsonify, request, flash, current_app, abort
from flask_login import login_required, current_user
from werkzeug.exceptions import NotFound
from app.utils.decorators import permission_required
from datetime import datetime, timedelta
from sqlalchemy import func

from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus, Payment
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.customers.models import Customer
from app.modules.tables.models_table import Table, TableStatus
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType, CashOutReason, PaymentMethod
from app.modules.inventory.models_stock_movement import StockMovementReason
from app.modules.settings.models_settings import Settings
from app.extensions import db
from . import bp

@bp.route('/')
@login_required
@permission_required('can_process_sales')
def index():
    products = Product.query.filter_by(owner_id=current_user.id).all()
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    return render_template('pos/index.html', products=products, categories=categories)

@bp.route('/sales')
@login_required
@permission_required('can_process_sales')
def sales():
    """Liste des ventes"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', None)
    date_from = request.args.get('date_from', None)
    date_to = request.args.get('date_to', None)
    
    # Base query
    query = Sale.query.filter_by(owner_id=current_user.id).order_by(Sale.created_at.desc())
    
    # Apply status filter if provided
    if status and status in [s.name for s in SaleStatus]:
        query = query.filter_by(status=SaleStatus[status])
    
    # Apply date filters if provided
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.created_at >= date_from)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Sale.created_at <= date_to + timedelta(days=1))
        except ValueError:
            pass
    
    # Get paginated results
    sales = query.paginate(page=page, per_page=10, error_out=False)
    
    # Create list of possible statuses for filter
    statuses = [(s.name, s.value) for s in SaleStatus]
    
    return render_template('pos/sales.html', 
                         sales=sales,
                         status=status,
                         date_from=date_from,
                         date_to=date_to,
                         statuses=statuses)

@bp.route('/sales/<int:id>')
@login_required
@permission_required('can_process_sales')
def sale_details(id):
    """Détails d'une vente"""
    sale = Sale.query.get_or_404(id)
    if sale.owner_id != current_user.id:
        abort(403)
    return render_template('pos/sale_details.html', sale=sale)

@bp.route('/sales/daily')
@login_required
@permission_required('can_process_sales')
def daily_sales():
    """Récapitulatif des ventes du jour"""
    today = datetime.now().date()
    
    # Get all paid sales for today
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).all()
    
    # Calculate totals by payment method
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments 
            if payment.method == method
        )
    
    # Calculate total sales
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen
    
    # Get hourly sales data for chart
    hourly_sales = db.session.query(
        func.strftime('%H', Sale.created_at).label('hour'),
        func.sum(Sale.total).label('total')
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by('hour').all()
    
    hourly_data = [0] * 24
    for hour, total in hourly_sales:
        hourly_data[int(hour)] = float(total or 0)
    
    # Produits les plus vendus aujourd'hui
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(5).all()
    
    return render_template('pos/daily_sales.html',
                         sales=sales,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         hourly_data=hourly_data,
                         top_products=top_products,
                         title="Ventes du jour")

@bp.route('/sales/stats')
@login_required
@permission_required('can_process_sales')
def sales_stats():
    """Statistiques des ventes"""
    # Récupérer les paramètres de filtre
    period = request.args.get('period', 'day')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    
    # Convertir les dates si fournies
    today = datetime.now().date()
    
    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today
    else:
        # Définir la date de début par défaut selon la période
        if period == 'day':
            start_date = today
        elif period == 'week':
            start_date = today - timedelta(days=6)  # Derniers 7 jours
        elif period == 'month':
            start_date = today.replace(day=1)  # Premier jour du mois
        elif period == 'year':
            start_date = today.replace(month=1, day=1)  # Premier jour de l'année
        else:
            start_date = today
    
    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            end_date = today
    else:
        end_date = today
    
    # Assurer que end_date est après start_date
    if end_date < start_date:
        end_date = start_date
    
    # Créer les objets datetime pour la requête
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    # Requête de base pour les ventes payées
    sales_query = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    )
    
    # Récupérer toutes les ventes dans la période
    sales = sales_query.all()
    
    # Calculer les totaux par méthode de paiement
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments 
            if payment.method == method
        )
    
    # Calculer le total des ventes et le nombre d'articles
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen
    
    # Calculer les ventes par jour pour le graphique
    daily_sales = {}
    for sale in sales:
        sale_date = sale.created_at.date()
        if sale_date not in daily_sales:
            daily_sales[sale_date] = 0
        daily_sales[sale_date] += sale.total
    
    # Préparer les données du graphique
    dates = []
    values = []
    
    # Remplir toutes les dates dans la plage, même celles sans ventes
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime('%Y-%m-%d'))
        values.append(daily_sales.get(current_date, 0))
        current_date += timedelta(days=1)
    
    # Calculer les ventes par heure pour aujourd'hui
    hourly_sales = {}
    if period == 'day' or period == 'today':
        for hour in range(24):
            hourly_sales[hour] = 0
        
        for sale in sales:
            if sale.created_at.date() == today:
                hour = sale.created_at.hour
                hourly_sales[hour] += sale.total
    
    # Calculer les ventes par jour de la semaine
    weekly_sales = []
    days_of_week = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
    weekly_totals = [0] * 7
    
    for sale in sales:
        day_idx = sale.created_at.weekday()  # 0 = Lundi, 6 = Dimanche
        weekly_totals[day_idx] += sale.total
    
    for i, day in enumerate(days_of_week):
        weekly_sales.append([day, weekly_totals[i]])
    
    # Calculer les ventes par mois
    monthly_sales = []
    months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
              'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
    monthly_totals = [0] * 12
    
    for sale in sales:
        month_idx = sale.created_at.month - 1  # 0 = Janvier, 11 = Décembre
        monthly_totals[month_idx] += sale.total
    
    for i, month in enumerate(months):
        monthly_sales.append([month, monthly_totals[i]])
    
    # Calculer les ventes par année
    yearly_sales = []
    current_year = datetime.now().year
    years = list(range(current_year - 4, current_year + 1))  # 5 dernières années
    yearly_totals = {year: 0 for year in years}
    
    for sale in sales:
        year = sale.created_at.year
        if year in yearly_totals:
            yearly_totals[year] += sale.total
    
    for year in sorted(yearly_totals.keys()):
        yearly_sales.append([str(year), yearly_totals[year]])
    
    # Créer un dictionnaire de ventes par jour (pour périodes personnalisées)
    sales_by_day = {}
    for date_str, value in zip(dates, values):
        sales_by_day[date_str] = value
    
    # Produits les plus vendus
    top_products = db.session.query(
        Product.name,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(10).all()
    
    return render_template('pos/sales_stats.html',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         dates=dates,
                         values=values,
                         top_products=top_products,
                         sales_count=len(sales),
                         hourly_sales=hourly_sales,
                         weekly_sales=weekly_sales,
                         monthly_sales=monthly_sales,
                         yearly_sales=yearly_sales,
                         sales_by_day=sales_by_day)

@bp.route('/process_pos_payment', methods=['POST'])
@login_required
def process_pos_payment():
    """Traitement du paiement depuis le POS"""
    # Get form data
    order_data = request.json
    
    if not order_data:
        return jsonify({'success': False, 'error': 'Données de commande invalides'})
    
    items = order_data.get('items', [])
    payments = order_data.get('payments', [])
    
    if not items:
        return jsonify({'success': False, 'error': 'La commande ne contient pas d\'articles'})
    
    if not payments:
        return jsonify({'success': False, 'error': 'Aucun paiement fourni'})
    
    # Get additional data
    table_id = order_data.get('table_id')
    customer_id = order_data.get('customer_id')
    discount_percentage = order_data.get('discount_percentage', 0)
    kitchen_note = order_data.get('kitchen_note', '')
    
    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        return jsonify({'success': False, 'error': 'Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.'})
    
    try:
        # Create sale record
        current_app.logger.info("Création d'une nouvelle vente...")
        sale = Sale(
            owner_id=current_user.id,
            user_id=current_user.id,
            status=SaleStatus.PAID,
            table_number=table_id,
            customer_id=customer_id,
            kitchen_note=kitchen_note
        )
        
        db.session.add(sale)
        
        # Add items to sale
        subtotal = 0
        current_app.logger.info(f"Ajout de {len(items)} articles au panier...")
        
        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = item_data.get('quantity', 1)
            price = item_data.get('price')
            
            # Verify product exists
            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Produit non trouvé: {product_id}'})
            
            # Verify we have enough stock if tracking is enabled
            if hasattr(product, 'track_inventory') and product.track_inventory:
                if hasattr(product, 'stock_quantity') and product.stock_quantity < quantity:
                    db.session.rollback()
                    return jsonify({'success': False, 'error': f'Stock insuffisant pour {product.name}'})
            
            # Add item to sale
            sale_item = SaleItem(
                sale=sale,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=price * quantity
            )
            db.session.add(sale_item)
            
            # Update stock
            try:
                current_app.logger.info(f"Mise à jour du stock pour {product.name}, quantité: {quantity}")
                
                # Cas 1: Produit avec recette
                if hasattr(product, 'recipe') and product.recipe is not None:
                    current_app.logger.info(f"Produit avec recette: {product.name}")
                    for recipe_item in product.recipe.items:
                        ingredient = recipe_item.ingredient
                        ingredient_qty_to_deduct = recipe_item.quantity * quantity
                        current_app.logger.info(f"Déduction de {ingredient_qty_to_deduct} de l'ingrédient {ingredient.name}")
                        ingredient.stock_quantity -= ingredient_qty_to_deduct
                
                # Cas 2: Produit simple - mise à jour directe du stock
                else:
                    current_app.logger.info(f"Mise à jour directe du stock pour {product.name}")
                    if hasattr(product, 'stock_quantity'):
                        product.stock_quantity -= quantity
                        current_app.logger.info(f"Nouveau stock: {product.stock_quantity}")
                
                # S'assurer que les modifications sont sauvegardées
                db.session.flush()
                
            except Exception as e:
                current_app.logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}", exc_info=True)
                # Continue processing - don't stop the sale for stock issues
            
            # Add to subtotal
            subtotal += price * quantity
        
        # Calculate total with discount
        if discount_percentage > 0:
            discount_amount = (subtotal * discount_percentage) / 100
            sale.discount_amount = discount_amount
            total = subtotal - discount_amount
        else:
            total = subtotal
        
        # Ensure total matches the sum of payments
        payment_total = sum(payment.get('amount', 0) for payment in payments)
        if abs(payment_total - total) > 0.01:  # Allow for small rounding differences
            db.session.rollback()
            return jsonify({
                'success': False, 
                'error': f'Le total des paiements ({payment_total}) ne correspond pas au total de la vente ({total})'
            })
        
        # Set sale totals
        sale.subtotal = subtotal
        sale.total = total
        
        # Process payments
        for payment_data in payments:
            payment_method_name = payment_data.get('method')
            amount = payment_data.get('amount')
            
            try:
                payment_method = PaymentMethod[payment_method_name]
            except KeyError:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Méthode de paiement invalide: {payment_method_name}'})
            
            # Add payment record
            payment = Payment(
                sale=sale,
                method=payment_method,
                amount=amount
            )
            db.session.add(payment)
            
            # Record cash register operation for this payment
            operation = CashOperation(
                register_id=cash_register.id,
                type=CashRegisterOperationType.SALE,
                amount=amount,
                payment_method=payment_method,
                user_id=current_user.id,
                owner_id=current_user.id,
                note=f"Vente #{sale.id}"
            )
            db.session.add(operation)
        
        # Update table status if applicable
        if table_id:
            table = Table.query.get(table_id)
            if table:
                table.reset_table()
        
        db.session.commit()
        
        # Return success response with sale ID
        return jsonify({
            'success': True, 
            'sale_id': sale.id,
            'message': 'Vente enregistrée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du traitement du paiement: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors du traitement: {str(e)}'})

@bp.route('/process_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_payment():
    """Process payment for a sale"""
    sale_id = request.form.get('sale_id')
    payment_method = request.form.get('payment_method')
    amount_tendered = float(request.form.get('amount_tendered', 0))
    
    sale = Sale.query.get_or_404(sale_id)
    
    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))
    
    # Check if sale is already paid
    if sale.status == SaleStatus.PAID:
        flash('Cette vente a déjà été payée.', 'warning')
        return redirect(url_for('pos.sale_details', id=sale_id))
    
    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        flash('Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.', 'error')
        return redirect(url_for('cash_register.open'))
    
    # Calculate change
    amount_due = sale.total
    change_amount = max(0, amount_tendered - amount_due) if amount_tendered > 0 else 0
    
    try:
        # Set sale as paid
        sale.status = SaleStatus.PAID
        sale.paid_at = datetime.utcnow()
        sale.cash_register_id = cash_register.id
        
        # Create payment record
        payment = Payment(
            sale=sale,
            method=PaymentMethod[payment_method],
            amount=amount_due,
            amount_tendered=amount_tendered if amount_tendered > 0 else amount_due,
            change_given=change_amount
        )
        db.session.add(payment)
        
        # Record cash register operation
        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=amount_due,
            payment_method=PaymentMethod[payment_method],
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Vente #{sale.id}"
        )
        db.session.add(operation)
        
        # Update product stock
        for item in sale.items:
            product = item.product
            if product and product.track_inventory:
                product.update_stock(item.quantity, operation='subtract', reason=StockMovementReason.SALE, reference=f"Sale #{sale.id}")
        
        # Update table status if applicable
        if sale.table:
            sale.table.reset_table()
        
        db.session.commit()
        
        flash('Paiement traité avec succès.', 'success')
        
        # Check if we should print a receipt
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        auto_print = settings.auto_print_receipt if settings else False
        
        if auto_print:
            return redirect(url_for('pos.print_receipt', sale_id=sale.id))
        else:
            return redirect(url_for('pos.sale_details', id=sale.id))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors du traitement du paiement: {str(e)}', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

@bp.route('/cancel_sale/<int:sale_id>', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def cancel_sale(sale_id):
    """Annuler une vente"""
    sale = Sale.query.get_or_404(sale_id)
    
    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))
    
    # Only allow cancellation of pending or kitchen pending sales
    if sale.status not in [SaleStatus.PENDING, SaleStatus.KITCHEN_PENDING]:
        flash('Seules les ventes en attente peuvent être annulées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))
    
    try:
        # Mark as cancelled
        sale.status = SaleStatus.CANCELLED
        sale.cancelled_at = datetime.utcnow()
        sale.cancelled_by_id = current_user.id
        
        # Free up the table if applicable
        if sale.table:
            sale.table.reset_table()
        
        db.session.commit()
        flash('Vente annulée avec succès.', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'annulation de la vente: {str(e)}', 'error')
    
    return redirect(url_for('pos.sales'))

@bp.route('/print_receipt/<int:sale_id>')
@login_required
@permission_required('can_process_sales')
def print_receipt(sale_id):
    """Imprimer un reçu de vente"""
    sale = Sale.query.get_or_404(sale_id)
    
    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))
    
    # Get store settings
    settings = Settings.query.filter_by(owner_id=current_user.id).first()
    
    return render_template('pos/receipt.html', 
                         sale=sale,
                         settings=settings)

@bp.route('/get_products/<int:category_id>')
@login_required
@permission_required('can_process_sales')
def get_products(category_id):
    """Get products by category for POS interface"""
    if category_id == 0:  # All products
        products = Product.query.filter_by(owner_id=current_user.id, is_active=True).all()
    else:
        products = Product.query.filter_by(
            owner_id=current_user.id,
            category_id=category_id,
            is_active=True
        ).all()
    
    products_data = []
    for product in products:
        # Calculate stock status
        stock_status = 'out_of_stock' if product.track_inventory and product.stock_quantity <= 0 else 'in_stock'
        
        products_data.append({
            'id': product.id,
            'name': product.name,
            'price': product.selling_price,
            'image_url': product.image_url or '/static/images/no-image.png',
            'stock_status': stock_status
        })
    
    return jsonify({'products': products_data})

@bp.route('/kitchen')
@login_required
@permission_required('can_access_kitchen')
def kitchen_orders():
    """Kitchen display system - show orders that need to be prepared"""
    # Get kitchen pending sales with unprepared items
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.KITCHEN_PENDING
    ).order_by(Sale.created_at).all()
    
    # Filter further in Python code to avoid SQL attribute errors
    # in case the column doesn't exist yet in the database
    filtered_sales = []
    for sale in sales:
        try:
            if sale.kitchen_status != 'completed':
                filtered_sales.append(sale)
        except:
            # If kitchen_status doesn't exist yet, include all pending sales
            filtered_sales.append(sale)
    
    return render_template('pos/kitchen.html', sales=filtered_sales)

@bp.route('/kitchen/order/<int:order_id>/ready', methods=['POST'])
@login_required
@permission_required('can_access_kitchen')
def mark_order_ready(order_id):
    """Mark a kitchen order as ready"""
    sale = Sale.query.get_or_404(order_id)
    
    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.kitchen_orders'))
    
    try:
        # Mark all items as prepared
        for item in sale.items:
            item.is_prepared = True
        
        # Update kitchen status on the sale
        sale.kitchen_status = 'completed'
        
        db.session.commit()
        flash('Commande marquée comme prête.', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur: {str(e)}', 'error')
    
    return redirect(url_for('pos.kitchen_orders'))

@bp.route('/sale/complete/<int:sale_id>', methods=['POST'])
@login_required
def complete_sale(sale_id):
    """Mark a sale as complete after delivery"""
    sale = Sale.query.get_or_404(sale_id)
    
    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))
    
    # Check if the sale can be completed
    if sale.status != SaleStatus.PAID:
        flash('Seules les ventes payées peuvent être marquées comme complétées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))
    
    try:
        # Mark as complete
        sale.status = SaleStatus.COMPLETED
        sale.completed_at = datetime.utcnow()
        
        db.session.commit()
        flash('Vente marquée comme complétée.', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur: {str(e)}', 'error')
    
    return redirect(url_for('pos.sale_details', id=sale_id)) 