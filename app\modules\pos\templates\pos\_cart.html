<div class="cart-container">
    <!-- Add the posSettings element with required configuration -->
    <div id="posSettings" data-require-table="false" class="d-none"></div>

    <!-- Sélection de table -->
    <div class="table-selection mb-3">
        <label for="tableSelect" class="form-label">
            <i class="fas fa-table"></i> Sélectionner une table
        </label>
        <select class="form-select" id="tableSelect">
            <option value="">Aucune table</option>
            {% for table in tables %}
            <option value="{{ table.id }}"
                    data-number="{{ table.number }}"
                    data-location="{{ table.location or '' }}"
                    data-capacity="{{ table.capacity }}"
                    data-status="{{ table.status }}"
                    {% if table.status != 'available' %}disabled{% endif %}>
                Table {{ table.number }}
                {% if table.location %}({{ table.location }}){% endif %}
                - {{ table.capacity }} pers.
                {% if table.status != 'available' %}
                    - {% if table.status == 'occupied' %}Occupée{% elif table.status == 'reserved' %}Réservée{% elif table.status == 'cleaning' %}Nettoyage{% endif %}
                {% endif %}
            </option>
            {% endfor %}
        </select>
        <div class="selected-table-info mt-2">
            <small id="selectedTableInfo" class="text-muted"></small>
        </div>
    </div>

    <div class="cart-items" id="cartItems">
        <!-- Les éléments du panier seront ajoutés ici dynamiquement -->
    </div>

    <div class="cart-total">
        Total: <span id="cartTotal">0.00 €</span>
    </div>

    <div class="cart-actions">
        <!-- Note pour la cuisine -->
        <div class="kitchen-note-section mb-3">
            <label for="kitchenNote" class="form-label">
                <i class="fas fa-sticky-note"></i> Note pour la cuisine
            </label>
            <textarea class="form-control" id="kitchenNote" rows="2"
                      placeholder="Instructions spéciales pour la cuisine..."></textarea>
        </div>

        <!-- Boutons d'action -->
        <button class="btn btn-warning btn-lg w-100 mb-2" onclick="POS.sendToKitchen()">
            <i class="fas fa-utensils"></i> Envoyer à la cuisine
        </button>
        <button class="btn btn-success btn-lg w-100 mb-2" onclick="POS.processPayment()">
            <i class="fas fa-cash-register"></i> Paiement direct
        </button>
        <div class="d-flex gap-2">
            <button class="btn btn-secondary flex-grow-1" onclick="POS.holdOrder()">
                <i class="fas fa-pause"></i> Pause
            </button>
            <button class="btn btn-danger flex-grow-1" onclick="POS.newOrder()">
                <i class="fas fa-plus"></i> Nouvelle
            </button>
        </div>
    </div>
</div>

<!-- Modal pour les commandes en attente -->
<div class="modal fade" id="heldOrdersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Commandes en attente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="heldOrdersList">
                    <!-- Les commandes en attente seront listées ici -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Paiement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="paymentTotal" class="form-label">Total</label>
                    <h4><span id="paymentTotal">0.00 €</span></h4>
                </div>
                <div class="mb-3">
                    <label class="form-label d-block">Mode de paiement</label>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-lg btn-success payment-method" data-method="cash">
                            <i class="fas fa-money-bill-wave"></i> Espèces
                        </button>
                        <button type="button" class="btn btn-lg btn-primary payment-method" data-method="card">
                            <i class="fas fa-credit-card"></i> Carte bancaire
                        </button>
                        <button type="button" class="btn btn-lg btn-info payment-method" data-method="check">
                            <i class="fas fa-money-check"></i> Chèque
                        </button>
                        <button type="button" class="btn btn-lg btn-secondary payment-method" data-method="other">
                            <i class="fas fa-ellipsis-h"></i> Autre
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.cart-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Styles pour la sélection de table */
.table-selection {
    background-color: white;
    border-radius: var(--radius);
    padding: 15px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.table-selection .form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.table-selection .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--radius);
    padding: 10px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.table-selection .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.table-selection .form-select option:disabled {
    color: #6c757d;
    background-color: #f8f9fa;
}

.selected-table-info {
    min-height: 20px;
}

#selectedTableInfo {
    font-style: italic;
    color: var(--success-color) !important;
    font-weight: 500;
}

/* Styles pour la section note de cuisine */
.kitchen-note-section {
    background-color: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: var(--radius);
    padding: 15px;
}

.kitchen-note-section .form-label {
    font-weight: 600;
    color: #f57c00;
    margin-bottom: 8px;
}

.kitchen-note-section .form-control {
    border: 2px solid #ffcc02;
    border-radius: var(--radius);
    font-size: 14px;
    resize: vertical;
}

.kitchen-note-section .form-control:focus {
    border-color: #f57c00;
    box-shadow: 0 0 0 0.2rem rgba(245, 124, 0, 0.25);
}

.cart-items {
    flex-grow: 1;
    overflow-y: auto;
    background-color: white;
    border-radius: var(--radius);
    margin-bottom: 10px;
    box-shadow: var(--shadow);
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    animation: fadeIn 0.3s ease-in-out;
}

.cart-item-details {
    flex-grow: 1;
}

.cart-item-actions {
    display: flex;
    gap: 5px;
}

.cart-total {
    font-size: 20px;
    font-weight: bold;
    padding: 15px;
    background-color: var(--light-color);
    border-radius: var(--radius);
    margin-top: 10px;
    box-shadow: var(--shadow);
}

.cart-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.cart-action-btn {
    padding: 10px;
    border-radius: var(--radius);
    border: none;
    color: white;
    font-weight: bold;
    transition: all 0.2s;
    cursor: pointer;
}

.cart-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.cart-action-btn.hold {
    background-color: var(--warning-color);
}

.cart-action-btn.new {
    background-color: var(--success-color);
}

.cart-action-btn.payment {
    background-color: var(--primary-color);
    grid-column: span 2;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes stockUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: var(--danger-color); }
    100% { transform: scale(1); }
}

.stock-update {
    animation: stockUpdate 0.5s ease-in-out;
}

@media (max-width: 1200px) {
    .cart-container {
        height: 400px;
    }
}
</style>