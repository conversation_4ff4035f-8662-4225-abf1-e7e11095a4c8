// Namespace pour le Point de Vente
const POS = {
    cart: [],
    heldOrders: [],
    currentTotal: 0,
    numpadValue: '0',
    isProcessingPayment: false, // Flag pour éviter les soumissions multiples

    init: function() {
        this.initEventListeners();
        this.initSearchFilter();
        this.initCategoryFilter();
        this.initProductGrid();
        this.initModals();
        this.clearDisplay();

        // Exposer la fonction processPayment globalement
        window.processPayment = (method) => {
            this.processPayment(method);
        };

        // S'assurer que l'affichage est initialisé correctement
        const display = document.getElementById('numpadDisplay');
        if (display) {
            display.textContent = '0';
        }
    },

    initEventListeners: function() {
        // Gestion du pavé numérique
        document.querySelectorAll('.numpad-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const value = btn.dataset.value;
                if (value === 'clear') {
                    this.clearDisplay();
                } else if (value === 'backspace') {
                    this.backspace();
                } else {
                    this.appendNumber(value);
                }
            });
        });

        // Gestion des boutons de paiement
        document.querySelectorAll('.payment-method').forEach(btn => {
            btn.addEventListener('click', () => {
                const method = btn.dataset.method;
                this.processPayment(method);
            });
        });

        // Gestion de la sélection de table
        const tableSelect = document.getElementById('tableSelect');
        if (tableSelect) {
            tableSelect.addEventListener('change', () => {
                const selectedOption = tableSelect.options[tableSelect.selectedIndex];
                const tableNumber = selectedOption.dataset.number;
                const tableLocation = selectedOption.dataset.location;
                const tableCapacity = selectedOption.dataset.capacity;
                const selectedTableInfo = document.getElementById('selectedTableInfo');

                if (selectedTableInfo) {
                    if (tableNumber) {
                        let infoText = `Table ${tableNumber}`;
                        if (tableLocation) {
                            infoText += ` (${tableLocation})`;
                        }
                        if (tableCapacity) {
                            infoText += ` - ${tableCapacity} personnes`;
                        }
                        selectedTableInfo.textContent = infoText;
                    } else {
                        selectedTableInfo.textContent = '';
                    }
                }
            });
        }

        // Gestion des clics sur les produits
        document.querySelectorAll('.product-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const productId = btn.dataset.productId;
                const name = btn.querySelector('.product-name').textContent;
                const priceText = btn.querySelector('.price').textContent;
                const price = parseFloat(priceText.replace('€', '').trim());
                const quantity = parseFloat(this.numpadValue) || 1;

                // Ajouter au panier
                const existingItem = this.cart.find(item => item.id === productId);
                if (existingItem) {
                    existingItem.quantity += quantity;
                    existingItem.total = existingItem.quantity * existingItem.price;
                } else {
                    this.cart.push({
                        id: productId,
                        name: name,
                        price: price,
                        quantity: quantity,
                        total: price * quantity
                    });
                }

                // Mettre à jour l'affichage
                this.updateCartDisplay();
                this.clearDisplay();
            });
        });
    },

    clearDisplay: function() {
        this.numpadValue = '0';
        const display = document.getElementById('numpadDisplay');
        if (display) {
            display.textContent = '0';
        }
    },

    backspace: function() {
        if (this.numpadValue.length > 1) {
            this.numpadValue = this.numpadValue.slice(0, -1);
        } else {
            this.numpadValue = '0';
        }
        const display = document.getElementById('numpadDisplay');
        if (display) {
            display.textContent = this.numpadValue;
        }
    },

    appendNumber: function(value) {
        if (this.numpadValue === '0' && value !== '.') {
            this.numpadValue = value;
        } else {
            if (value === '.' && this.numpadValue.includes('.')) {
                return;
            }
            this.numpadValue += value;
        }
        const display = document.getElementById('numpadDisplay');
        if (display) {
            display.textContent = this.numpadValue;
        }
    },

    processPayment: function(method) {
        const tableSelect = document.getElementById('tableSelect');
        const settings = document.getElementById('posSettings');

        // Éviter les soumissions multiples
        if (this.isProcessingPayment) {
            console.log('Paiement déjà en cours, évitement de la duplication');
            return;
        }

        // Check if settings exists before trying to access its properties
        if (settings && settings.dataset.requireTable === 'true' && tableSelect && !tableSelect.value) {
            this.showAlert('error', 'Veuillez sélectionner une table avant de procéder au paiement.');
            return;
        }

        if (this.cart.length === 0) {
            this.showAlert('error', 'Le panier est vide');
            return;
        }

        // Si la méthode n'est pas fournie, afficher le modal de paiement
        if (!method) {
            const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
            // Mettre à jour le total dans la modale
            const total = this.cart.reduce((sum, item) => sum + item.total, 0);
            document.getElementById('paymentTotal').textContent = total.toFixed(2) + ' €';
            paymentModal.show();
            return;
        }

        // S'assurer que la méthode est en majuscules pour correspondre à l'énumération PaymentMethod
        const paymentMethod = method.toUpperCase();

        // Fermer le modal de paiement s'il est ouvert
        const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        if (paymentModal) {
            paymentModal.hide();
        }

        // Définir le flag pour éviter les soumissions multiples
        this.isProcessingPayment = true;
        this.showAlert('info', 'Traitement du paiement en cours...');

        const total = this.cart.reduce((sum, item) => sum + item.total, 0);
        const data = {
            items: this.cart.map(item => ({
                product_id: item.id,
                quantity: item.quantity,
                price: item.price
            })),
            payment_method: paymentMethod,
            payments: [{
                method: paymentMethod,
                amount: total
            }],
            table_id: (tableSelect && tableSelect.value) || null
        };

        console.log('Sending payment data:', JSON.stringify(data));

        fetch('/pos/process_pos_payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            this.isProcessingPayment = false; // Réinitialiser le flag
            if (data.success) {
                this.showAlert('success', 'Paiement effectué avec succès');
                this.clearCart();
                // Rediriger vers la page de détails de la vente
                window.location.href = `/pos/sales/${data.sale_id}`;
            } else {
                this.showAlert('error', data.error || 'Erreur lors du paiement');
            }
        })
        .catch(error => {
            this.isProcessingPayment = false; // Réinitialiser le flag en cas d'erreur
            console.error('Error:', error);
            this.showAlert('error', 'Erreur lors du paiement');
        });
    },

    holdOrder: function() {
        if (this.cart.length === 0) {
            this.showAlert('error', 'Le panier est vide');
            return;
        }

        const tableSelect = document.getElementById('tableSelect');
        const tableInfo = tableSelect && tableSelect.value ?
            `Table ${tableSelect.options[tableSelect.selectedIndex].dataset.number}` :
            'Sans table';

        this.heldOrders.push({
            items: [...this.cart],
            table: tableSelect ? tableSelect.value : null,
            tableInfo: tableInfo,
            timestamp: new Date().toLocaleTimeString()
        });

        this.clearCart();
        this.updateHeldOrdersList();
        this.showAlert('success', 'Commande mise en pause');
    },

    newOrder: function() {
        if (this.cart.length > 0) {
            if (!confirm('Êtes-vous sûr de vouloir commencer une nouvelle commande ? La commande actuelle sera perdue.')) {
                return;
            }
        }
        this.clearCart();
        const tableSelect = document.getElementById('tableSelect');
        const selectedTableInfo = document.getElementById('selectedTableInfo');
        if (tableSelect) {
            tableSelect.value = '';
        }
        if (selectedTableInfo) {
            selectedTableInfo.textContent = '';
        }
        this.clearDisplay();
    },

    clearCart: function() {
        this.cart = [];
        this.updateCartDisplay();
    },

    updateCartDisplay: function() {
        const cartItems = document.getElementById('cartItems');
        const cartTotal = document.getElementById('cartTotal');

        if (!cartItems || !cartTotal) return;

        cartItems.innerHTML = '';
        let total = 0;

        this.cart.forEach((item, index) => {
            const row = document.createElement('div');
            row.className = 'cart-item';
            row.innerHTML = `
                <div class="cart-item-details">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${item.price.toFixed(2)} €</div>
                </div>
                <div class="cart-item-actions">
                    <input type="number" class="form-control form-control-sm quantity-input"
                           value="${item.quantity}" min="1" style="width: 60px; display: inline-block;">
                    <button class="btn btn-sm btn-outline-primary update-cart-item" onclick="POS.updateCartItem(${index})">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="POS.removeFromCart(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            cartItems.appendChild(row);
            total += item.price * item.quantity;
        });

        cartTotal.textContent = total.toFixed(2) + ' €';
    },

    removeFromCart: function(index) {
        this.cart.splice(index, 1);
        this.updateCartDisplay();
    },

    editQuantity: function(index) {
        const item = this.cart[index];
        const newQuantity = parseFloat(this.numpadValue);

        if (!newQuantity || newQuantity <= 0) {
            this.showAlert('error', 'Quantité invalide');
            return;
        }

        item.quantity = newQuantity;
        item.total = item.quantity * item.price;
        this.updateCartDisplay();
        this.clearDisplay();
    },

    showAlert: function(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => alertDiv.remove(), 3000);
    },

    updateHeldOrdersList: function() {
        const list = document.getElementById('heldOrdersList');
        if (!list) return;

        list.innerHTML = '';
        this.heldOrders.forEach((order, index) => {
            const div = document.createElement('div');
            div.className = 'held-order-item p-2 border-bottom';
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${order.tableInfo}</strong>
                        <small class="text-muted">${order.timestamp}</small>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="POS.restoreOrder(${index})">
                        Restaurer
                    </button>
                </div>
            `;
            list.appendChild(div);
        });
    },

    restoreOrder: function(index) {
        if (this.cart.length > 0) {
            if (!confirm('Êtes-vous sûr de vouloir restaurer cette commande ? La commande actuelle sera perdue.')) {
                return;
            }
        }

        const order = this.heldOrders[index];
        this.cart = order.items;
        const tableSelect = document.getElementById('tableSelect');
        if (order.table && tableSelect) {
            tableSelect.value = order.table;
            // Déclencher l'événement change pour mettre à jour l'affichage
            const event = new Event('change');
            tableSelect.dispatchEvent(event);
        }

        this.heldOrders.splice(index, 1);
        this.updateCartDisplay();
        this.updateHeldOrdersList();

        const modal = document.getElementById('heldOrdersModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    },

    initSearchFilter: function() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                document.querySelectorAll('.product-btn').forEach(product => {
                    const name = product.querySelector('.product-name').textContent.toLowerCase();
                    product.style.display = name.includes(searchTerm) ? '' : 'none';
                });
            });
        }
    },

    initCategoryFilter: function() {
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Retirer la classe active de tous les onglets
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                const categoryId = tab.dataset.category;
                document.querySelectorAll('.product-btn').forEach(product => {
                    if (categoryId === 'all' || product.dataset.category === categoryId) {
                        product.style.display = '';
                    } else {
                        product.style.display = 'none';
                    }
                });
            });
        });
    },

    initProductGrid: function() {
        // Cette fonction peut être utilisée pour initialiser des fonctionnalités supplémentaires de la grille
    },

    initModals: function() {
        // Cette fonction peut être utilisée pour initialiser les modals Bootstrap
    },

    handleResize: function() {
        // Cette fonction peut être utilisée pour gérer le redimensionnement de la fenêtre
    },

    updateCartItem: function(index) {
        const item = this.cart[index];
        if (!item) return;

        const quantityInput = document.querySelectorAll('.quantity-input')[index];
        const newQuantity = parseFloat(quantityInput.value);

        if (isNaN(newQuantity) || newQuantity < 1) {
            this.showAlert('error', 'La quantité doit être un nombre positif');
            quantityInput.value = item.quantity;
            return;
        }

        item.quantity = newQuantity;
        item.total = item.price * newQuantity;
        this.updateCartDisplay();
        this.showAlert('success', 'Quantité mise à jour');
    }
};

// Initialize the POS object when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof POS !== 'undefined' && typeof POS.init === 'function') {
        POS.init();
        console.log('POS system initialized');

        // Gérer le redimensionnement de la fenêtre
        window.addEventListener('resize', () => POS.handleResize());
        POS.handleResize(); // État initial
    } else {
        console.error('Failed to initialize POS system');
    }
});