#!/usr/bin/env python3
"""
Migration pour ajouter la colonne table_id à la table sales
Compatible avec SQLite
"""

import sqlite3
import os

def migrate_database():
    # Chemin vers la base de données
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à {db_path}")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne table_id existe déjà
        cursor.execute("PRAGMA table_info(sales)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'table_id' in columns:
            print("La colonne table_id existe déjà dans la table sales")
            conn.close()
            return True
        
        print("Ajout de la colonne table_id à la table sales...")
        
        # Ajouter la colonne table_id
        cursor.execute("ALTER TABLE sales ADD COLUMN table_id INTEGER")
        
        # Mettre à jour les enregistrements existants
        # Convertir table_number vers table_id si c'est un nombre
        cursor.execute("""
            UPDATE sales 
            SET table_id = CAST(table_number AS INTEGER)
            WHERE table_number IS NOT NULL 
            AND table_number != ''
            AND table_number GLOB '[0-9]*'
        """)
        
        # Valider les changements
        conn.commit()
        
        print("Migration réussie ! Colonne table_id ajoutée à la table sales")
        
        # Vérifier le résultat
        cursor.execute("SELECT COUNT(*) FROM sales WHERE table_id IS NOT NULL")
        count = cursor.fetchone()[0]
        print(f"Nombre d'enregistrements avec table_id mis à jour: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Erreur lors de la migration: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n✅ Migration terminée avec succès!")
        print("Vous pouvez maintenant redémarrer l'application.")
    else:
        print("\n❌ Échec de la migration.")
