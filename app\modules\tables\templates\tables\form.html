{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.number.label(class="form-label") }}
                                    {{ form.number(class="form-control") }}
                                    {% if form.number.errors %}
                                        {% for error in form.number.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.capacity.label(class="form-label") }}
                                    {{ form.capacity(class="form-control") }}
                                    {% if form.capacity.errors %}
                                        {% for error in form.capacity.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-select") }}
                                    {% if form.location.errors %}
                                        {% for error in form.location.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('tables.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 