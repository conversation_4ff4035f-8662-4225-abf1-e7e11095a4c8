#!/usr/bin/env python3
"""
Migration pour ajouter la colonne table_id à la table sales
"""

from app import create_app, db
from sqlalchemy import text

def add_table_id_column():
    app = create_app()
    
    with app.app_context():
        try:
            # Vérifier si la colonne existe déjà
            result = db.session.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='sales' AND column_name='table_id'
            """)).fetchone()
            
            if result:
                print("La colonne table_id existe déjà dans la table sales")
                return
            
            # Ajouter la colonne table_id
            db.session.execute(text("""
                ALTER TABLE sales 
                ADD COLUMN table_id INTEGER REFERENCES tables(id)
            """))
            
            # Mettre à jour les enregistrements existants
            # Convertir table_number (qui contient l'ID) vers table_id
            db.session.execute(text("""
                UPDATE sales 
                SET table_id = CAST(table_number AS INTEGER)
                WHERE table_number IS NOT NULL 
                AND table_number ~ '^[0-9]+$'
            """))
            
            db.session.commit()
            print("Colonne table_id ajoutée avec succès à la table sales")
            
        except Exception as e:
            db.session.rollback()
            print(f"Erreur lors de l'ajout de la colonne table_id: {str(e)}")
            # Pour SQLite, essayons une approche différente
            try:
                # Ajouter simplement la colonne sans contrainte de clé étrangère
                db.session.execute(text("ALTER TABLE sales ADD COLUMN table_id INTEGER"))
                
                # Mettre à jour les enregistrements existants
                db.session.execute(text("""
                    UPDATE sales 
                    SET table_id = CAST(table_number AS INTEGER)
                    WHERE table_number IS NOT NULL 
                    AND table_number != ''
                """))
                
                db.session.commit()
                print("Colonne table_id ajoutée avec succès (SQLite)")
                
            except Exception as e2:
                db.session.rollback()
                print(f"Erreur SQLite: {str(e2)}")

if __name__ == "__main__":
    add_table_id_column()
