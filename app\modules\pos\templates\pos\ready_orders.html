{% extends "base.html" %}

{% block title %}Commandes Prêtes{% endblock %}

{% block extra_css %}
<!-- Le token CSRF est déjà défini dans base.html -->
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-bell text-success"></i> Commandes Prêtes à Servir
        </h2>
        <div>
            <a href="{{ url_for('pos.kitchen_orders') }}" class="btn btn-warning me-2">
                <i class="fas fa-utensils"></i> Cuisine
            </a>
            <a href="{{ url_for('pos.index') }}" class="btn btn-primary">
                <i class="fas fa-cash-register"></i> POS
            </a>
        </div>
    </div>

    {% if orders %}
    <div class="row">
        {% for order in orders %}
        <div class="col-md-4 mb-4">
            <div class="card border-success h-100">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-receipt"></i> Commande #{{ order.reference }}
                    </h6>
                    <small>{{ order.created_at.strftime('%H:%M') }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations de table -->
                    {% if order.table %}
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-table"></i>
                        <strong>Table {{ order.table.number }}</strong>
                        {% if order.table.location %}({{ order.table.location }}){% endif %}
                    </div>
                    {% endif %}

                    <!-- Articles de la commande -->
                    <h6 class="text-muted mb-2">Articles:</h6>
                    <ul class="list-group list-group-flush mb-3">
                        {% for item in order.items %}
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>{{ item.product.name }}</span>
                            <span class="badge bg-primary rounded-pill">{{ item.quantity }}</span>
                        </li>
                        {% endfor %}
                    </ul>

                    <!-- Note de cuisine -->
                    {% if order.kitchen_note %}
                    <div class="alert alert-warning mb-3">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-sticky-note"></i> Note:
                        </h6>
                        <p class="mb-0">{{ order.kitchen_note }}</p>
                    </div>
                    {% endif %}

                    <!-- Total -->
                    <div class="text-center">
                        <h5 class="text-success">
                            <i class="fas fa-euro-sign"></i> {{ "%.2f"|format(order.total) }} €
                        </h5>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-grid gap-2">
                        <!-- Formulaire de paiement direct -->
                        <form action="{{ url_for('pos.process_payment') }}" method="POST" style="display: inline;">
                            {{ csrf_token() }}
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="sale_id" value="{{ order.id }}">
                            <input type="hidden" name="payment_method" value="CASH">
                            <input type="hidden" name="amount_tendered" value="{{ order.total }}">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-cash-register"></i> Encaisser (Espèces)
                            </button>
                        </form>
                        <button type="button" class="btn btn-warning w-100" onclick="processPayment({{ order.id }})">
                            <i class="fas fa-credit-card"></i> Autre méthode
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="markAsDelivered({{ order.id }})">
                            <i class="fas fa-check"></i> Marquer comme servi
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <div class="alert alert-info">
            <i class="fas fa-info-circle fa-3x mb-3"></i>
            <h4>Aucune commande prête</h4>
            <p class="mb-0">Toutes les commandes ont été servies ou sont encore en préparation.</p>
        </div>
        <a href="{{ url_for('pos.kitchen_orders') }}" class="btn btn-warning">
            <i class="fas fa-utensils"></i> Voir la cuisine
        </a>
    </div>
    {% endif %}
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Encaisser la commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" id="saleId" name="sale_id">
                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Méthode de paiement</label>
                        <select class="form-select" id="paymentMethod" name="payment_method" required>
                            <option value="CASH">Espèces</option>
                            <option value="CARD">Carte</option>
                            <option value="CHECK">Chèque</option>
                        </select>
                    </div>
                    <div class="mb-3" id="amountTenderedGroup">
                        <label for="amountTendered" class="form-label">Montant reçu</label>
                        <input type="number" class="form-control" id="amountTendered" name="amount_tendered" step="0.01" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="submitPayment()">
                    <i class="fas fa-cash-register"></i> Encaisser
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentOrderTotal = 0;

function processPayment(orderId) {
    // Récupérer les détails de la commande
    const orderCard = document.querySelector(`button[onclick="processPayment(${orderId})"]`).closest('.card');
    const totalText = orderCard.querySelector('.text-success').textContent;
    currentOrderTotal = parseFloat(totalText.match(/[\d.]+/)[0]);

    // Remplir le modal
    document.getElementById('saleId').value = orderId;
    document.getElementById('amountTendered').value = currentOrderTotal;

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function submitPayment() {
    console.log('submitPayment called');
    const form = document.getElementById('paymentForm');
    const formData = new FormData(form);

    // Convertir FormData en objet JSON
    const data = {
        sale_id: formData.get('sale_id'),
        payment_method: formData.get('payment_method'),
        amount_tendered: parseFloat(formData.get('amount_tendered')) || currentOrderTotal,
        csrf_token: formData.get('csrf_token')
    };

    console.log('Payment data:', data);

    // Vérifier le token CSRF
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    console.log('CSRF token element:', csrfToken);
    console.log('CSRF token value:', csrfToken ? csrfToken.content : 'NOT FOUND');

    fetch('/pos/process_ready_payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken ? csrfToken.content : ''
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
            if (modal) modal.hide();

            // Afficher un message de succès
            if (data.change > 0) {
                alert(`Paiement réussi ! Monnaie à rendre : ${data.change.toFixed(2)} €`);
            } else {
                alert('Paiement traité avec succès !');
            }

            // Recharger la page
            location.reload();
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        alert('Erreur de connexion: ' + error.message);
    });
}

function markAsDelivered(orderId) {
    if (confirm('Marquer cette commande comme servie ?')) {
        fetch(`/pos/kitchen/order/${orderId}/delivered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur de connexion');
        });
    }
}

// Gestion de l'affichage du champ "Montant reçu"
document.getElementById('paymentMethod').addEventListener('change', function() {
    const amountTenderedGroup = document.getElementById('amountTenderedGroup');
    if (this.value === 'CASH') {
        amountTenderedGroup.style.display = 'block';
        document.getElementById('amountTendered').required = true;
    } else {
        amountTenderedGroup.style.display = 'none';
        document.getElementById('amountTendered').required = false;
        document.getElementById('amountTendered').value = currentOrderTotal;
    }
});

// Auto-refresh every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
