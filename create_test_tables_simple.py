#!/usr/bin/env python3
"""
Script simple pour créer des tables de test via l'interface web
"""

import requests
import json

def create_tables_via_api():
    base_url = "http://127.0.0.1:5000"
    
    # D'abord, nous devons nous connecter pour obtenir une session
    session = requests.Session()
    
    # Obtenir la page de connexion pour récupérer le token CSRF
    login_page = session.get(f"{base_url}/auth/login")
    
    if login_page.status_code != 200:
        print("Impossible d'accéder à la page de connexion")
        return
    
    print("Veuillez vous connecter manuellement via l'interface web")
    print("Puis créer des tables via l'interface de gestion des tables")
    print("URL: http://127.0.0.1:5000/tables/new")

if __name__ == "__main__":
    create_tables_via_api()
