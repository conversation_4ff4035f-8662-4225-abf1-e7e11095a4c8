<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
            <i class="fas fa-cash-register"></i>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            {% if current_user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <!-- Tableau de bord -->
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}"> Dashboard
                            <i class="fas fa-tachometer-alt"></i>
                        </a>
                    </li>

                    <!-- Point de vente -->
                    {% if current_user.can_process_sales %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-shopping-cart"></i> Point de vente
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('pos.index') }}">
                                        <i class="fas fa-cash-register"></i> Caisse
                                    </a>
                                </li>
                                {% if current_user.can_access_cash_register() %}
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('cash_register.index') }}">
                                            <i class="fas fa-wallet"></i> État de la caisse
                                        </a>
                                    </li>
                                    
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('cash_register.history') }}">
                                            <i class="fas fa-history"></i> Historique de caisse
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('cash_register.settings') }}">
                                            <i class="fas fa-cog"></i> Paramètres de caisse
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('settings.cash_register') }}">
                                            <i class="fas fa-cog"></i> Autres Paramètres de caisse
                                        </a>
                                    </li>
                                    {% if current_user.can_manage_cash_register() %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#cashInModal">
                                                <i class="fas fa-sign-in-alt"></i> Entrée de caisse
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#cashOutModal">
                                                <i class="fas fa-sign-out-alt"></i> Sortie de caisse
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#bankDepositModal">
                                                <i class="fas fa-university"></i> Versement en banque
                                            </a>
                                        </li>
                                    {% endif %}
                                {% endif %}
                                {% if current_user.can_access_kitchen %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('pos.kitchen_orders') }}">
                                            <i class="fas fa-utensils"></i> Commandes cuisine
                                        </a>
                                    </li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                {% if current_user.can_manage_customers %}
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('customers.index') }}">
                                            <i class="fas fa-users"></i> Clients
                                        </a>
                                    </li>
                                {% endif %}
                                {% if current_user.can_manage_promotions %}
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('promotions.index') }}">
                                            <i class="fas fa-percent"></i> Promotions
                                        </a>
                                    </li>
                                {% endif %}
                                {% if current_user.can_manage_tables %}
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('tables.index') }}">
                                            <i class="fas fa-chair"></i> Tables
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </li>
                    {% endif %}
                    
                    <!-- Inventaire -->
                    {% if current_user.can_manage_inventory %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-boxes"></i> Inventaire
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.products') }}">
                                        <i class="fas fa-box"></i> Produits
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.ingredients') }}">
                                        <i class="fas fa-carrot"></i> Ingrédients
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.products_recipes') }}">
                                        <i class="fas fa-book-open"></i> Fiches Techniques
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.product_categories') }}">
                                        <i class="fas fa-tags"></i> Catégories de produits
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.ingredient_categories') }}">
                                        <i class="fas fa-tags"></i> Catégories d'ingrédients
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('inventory.suppliers') }}">
                                        <i class="fas fa-truck"></i> Fournisseurs
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.inventory') }}">
                                        <i class="fas fa-chart-bar"></i> Rapports d'inventaire
                                    </a>
                                </li>
                            </ul>
                        </li>
                    {% endif %}
                    
                    <!-- Dépenses -->
                    {% if current_user.can_manage_expenses %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-wallet"></i> Dépenses
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('expenses.index') }}">
                                        <i class="fas fa-list"></i> Liste des dépenses
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('expenses.create') }}">
                                        <i class="fas fa-plus"></i> Nouvelle dépense
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('expenses.categories') }}">
                                        <i class="fas fa-tags"></i> Catégories
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.expenses') }}">
                                        <i class="fas fa-money-bill"></i> Rapport des dépenses
                                    </a>
                                </li>
                            </ul>
                        </li>
                    {% endif %}
                    
                    <!-- Rapports -->
                    {% if current_user.can_access_reports %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-chart-bar"></i> Rapports
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.index') }}">
                                        <i class="fas fa-tachometer-alt"></i> Vue d'ensemble
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.dashboard') }}">
                                        <i class="fas fa-chart-line"></i> Rapports de ventes 1
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.sales') }}">
                                        <i class="fas fa-chart-line"></i> Rapports de ventes 2
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('pos.sales') }}">
                                        <i class="fas fa-list"></i> Liste des ventes
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('pos.daily_sales') }}">
                                        <i class="fas fa-calendar-day"></i> Ventes du jour
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('pos.sales_stats') }}">
                                        <i class="fas fa-chart-line"></i> Statistiques des ventes
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.products') }}">
                                        <i class="fas fa-box"></i> Produits
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('reports.profit_loss') }}">
                                        <i class="fas fa-percent"></i> Bilan
                                    </a>
                                </li>                                                               
                            </ul>
                        </li>
                    {% endif %}

                    <!-- Paramètres -->
                    {% if current_user.is_owner or current_user.is_admin or current_user.is_system_admin %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i> Paramètres
                            </a>
                            <ul class="dropdown-menu">
                                                               
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('settings.receipt') }}">
                                        <i class="fas fa-receipt"></i> Ticket de caisse
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('settings.payment') }}">
                                        <i class="fas fa-credit-card"></i> Paiement et Taxes
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('settings.sales') }}">
                                        <i class="fas fa-shopping-cart"></i> Ventes
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('advanced.api_settings') }}">
                                        <i class="fas fa-plug"></i> API & Intégrations
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('advanced.import_export') }}">
                                        <i class="fas fa-exchange-alt"></i> Import/Export
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('advanced.customization') }}">
                                        <i class="fas fa-paint-brush"></i> Personnalisation
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('advanced.automation') }}">
                                        <i class="fas fa-robot"></i> Automatisation
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('advanced.notifications') }}">
                                        <i class="fas fa-bell"></i> Notifications
                                    </a>
                                </li>                                
                            </ul>
                        </li>
                    {% endif %}

                    <!-- Admin Menu -->
                    {% if current_user.is_system_admin or current_user.is_system_admin %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-user-shield"></i> Système Admin
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="adminDropdown">
                                <a class="dropdown-item" href="{{ url_for('admin.system_users') }}">
                                    <i class="fas fa-users fa-sm fa-fw mr-2 text-gray-400"></i> Gestion des Utilisateurs
                                </a>
                               
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('admin.settings') }}">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i> Paramètres Système
                                </a>
                            </div>
                        </li>
                    {% endif %}

                    <!-- Owner Menu -->
                    {% if current_user.is_owner or current_user.is_admin or current_user.is_system_admin %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="ownerDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-user-tie"></i> Administration
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="ownerDropdown">
                                <a class="dropdown-item" href="{{ url_for('admin.owner_users') }}">
                                    <i class="fas fa-users fa-sm fa-fw mr-2 text-gray-400"></i> Gestion des Utilisateurs
                                </a>
                                <a class="dropdown-item" href="{{ url_for('admin.owner_roles') }}">
                                    <i class="fas fa-user-tag fa-sm fa-fw mr-2 text-gray-400"></i> Gestion des Rôles
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('admin.owner_settings') }}">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i> Paramètres
                                </a>
                                <a class="dropdown-item" href="{{ url_for('admin.owner_business') }}">
                                    <i class="fas fa-building fa-sm fa-fw mr-2 text-gray-400"></i> Informations Entreprise
                                </a>
                            </div>
                        </li>
                    {% endif %}
                </ul>

                <!-- Menu utilisateur -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('main.profile') }}">
                                    <i class="fas fa-id-card"></i> Profil
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            {% else %}
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt"></i> Connexion
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus"></i> Inscription
                        </a>
                    </li>
                </ul>
            {% endif %}
        </div>
    </div>
</nav>